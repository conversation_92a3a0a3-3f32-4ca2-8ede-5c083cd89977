#!/bin/bash

# 多进程+多线程优化的游戏服务器启动脚本
# 结合多进程和多线程，最大化利用8核16G服务器性能

# 配置参数
LOG_DIR="/data/server/logs"
PID_DIR="/data/server/pids"
MONITOR_DIR="/data/server/monitor"

# 创建必要目录
mkdir -p "$LOG_DIR" "$PID_DIR" "$MONITOR_DIR"

# 多进程+多线程配置
# 针对8核16G服务器的最优配置
BACKEND_PROCESSES=3      # 后台服务3个进程
FIGHT_PROCESSES=3        # 战斗服务3个进程
ZONE_PROCESSES_PER_ZONE=2  # 每个区服2个进程

# 系统级优化设置
echo "应用系统级优化设置..."

# 增加文件描述符限制
ulimit -n 65536

# 增加进程数限制
ulimit -u 32768

# 设置TCP优化参数（需要root权限）
if [ "$EUID" -eq 0 ]; then
    echo "应用TCP优化参数..."
    # 增加TCP连接队列长度
    echo 16384 > /proc/sys/net/core/somaxconn
    # 优化TCP时间等待
    echo 1 > /proc/sys/net/ipv4/tcp_tw_reuse
    echo 1 > /proc/sys/net/ipv4/tcp_tw_recycle
    # 增加TCP缓冲区
    echo "4096 87380 33554432" > /proc/sys/net/ipv4/tcp_rmem
    echo "4096 65536 33554432" > /proc/sys/net/ipv4/tcp_wmem
    # 增加网络设备队列长度
    echo 5000 > /proc/sys/net/core/netdev_max_backlog
fi

# 区服配置
declare -A ZONE_CONFIG
ZONE_CONFIG[1]="5001"    # 1区 - 端口5001
ZONE_CONFIG[4]="5004"    # 4区 - 端口5004  
ZONE_CONFIG[5]="5005"    # 5区 - 端口5005

# 多进程端口配置
# 后台服务端口范围：8500-8502
BACKEND_BASE_PORT=8500

# 战斗服务端口范围：3001-3003
FIGHT_BASE_PORT=3001

# 区服多进程端口配置
declare -A ZONE_PROCESS_PORTS
ZONE_PROCESS_PORTS[1]="5001,5011"  # 1区：5001和5011端口
ZONE_PROCESS_PORTS[4]="5004,5014"  # 4区：5004和5014端口
ZONE_PROCESS_PORTS[5]="5005,5015"  # 5区：5005和5015端口

# 区服启动顺序
ZONE_START_ORDER=(1 4 5)

echo "多进程+多线程配置："
echo "  后台服务: $BACKEND_PROCESSES 个进程"
echo "  战斗服务: $FIGHT_PROCESSES 个进程"
echo "  区服: 每区 $ZONE_PROCESSES_PER_ZONE 个进程"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

log_warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARN: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

# 检查进程是否运行
is_running() {
    local name="$1"
    local pid_file="$PID_DIR/${name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# 启动服务函数
start_service() {
    local name="$1"
    local cmd="$2"
    local port="$3"
    local log_file="$LOG_DIR/${name}.log"
    local pid_file="$PID_DIR/${name}.pid"
    local monitor_file="$MONITOR_DIR/${name}.monitor"

    log_info "检查服务 [$name]"

    if is_running "$name"; then
        log_warn "$name 已在运行，跳过启动"
        return 0
    fi

    log_info "启动 $name (端口: $port)..."
    
    # 启动服务并记录PID
    nohup $cmd >> "$log_file" 2>&1 &
    local pid=$!
    echo $pid > "$pid_file"
    
    # 等待服务启动
    sleep 2
    
    if kill -0 "$pid" 2>/dev/null; then
        log_info "$name 启动成功！PID: $pid"

        # 创建监控文件
        cat > "$monitor_file" << EOF
{
    "name": "$name",
    "pid": $pid,
    "port": $port,
    "start_time": "$(date '+%Y-%m-%d %H:%M:%S')",
    "command": "$cmd",
    "log_file": "$log_file"
}
EOF
        return 0
    else
        log_error "$name 启动失败！查看日志: tail -10 $log_file"
        rm -f "$pid_file"
        return 1
    fi
}

# 启动多个后台服务进程
start_backend_processes() {
    log_info "启动多个后台服务进程..."
    
    for ((i=0; i<$BACKEND_PROCESSES; i++)); do
        local port=$((BACKEND_BASE_PORT + i))
        local service_name="Backend_${port}"
        local cmd="python /data/server/trunk/llol/src/manage.py runserver 0.0.0.0:${port}"
        
        start_service "$service_name" "$cmd" "$port"
        sleep 1  # 避免同时启动造成资源竞争
    done
}

# 启动多个战斗服务进程
start_fight_processes() {
    log_info "启动多个战斗服务进程..."
    
    cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }
    
    for ((i=0; i<$FIGHT_PROCESSES; i++)); do
        local port=$((FIGHT_BASE_PORT + i))
        local service_name="Fight_${port}"
        local cmd="node Laya.js ${port}"
        
        start_service "$service_name" "$cmd" "$port"
        sleep 1  # 避免同时启动造成资源竞争
    done
}

# 启动区服多进程
start_zone_processes() {
    log_info "启动区服多进程..."
    
    cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }
    
    for zone_id in "${ZONE_START_ORDER[@]}"; do
        if [[ -n "${ZONE_PROCESS_PORTS[$zone_id]}" ]]; then
            local ports_str="${ZONE_PROCESS_PORTS[$zone_id]}"
            IFS=',' read -ra ports <<< "$ports_str"
            
            log_info "启动${zone_id}区多进程服务器..."
            
            for ((i=0; i<${#ports[@]}; i++)); do
                local port="${ports[$i]}"
                local service_name="Zone_${zone_id}_${port}"
                
                # 使用多线程版本的服务器
                local cmd="python server_multithreaded.py --zone=${zone_id} --port=${port}"
                
                start_service "$service_name" "$cmd" "$port"
                sleep 2  # 避免数据库连接竞争
            done
        fi
    done
}

# 停止服务函数
stop_service() {
    local name="$1"
    local pid_file="$PID_DIR/${name}.pid"
    local monitor_file="$MONITOR_DIR/${name}.monitor"

    log_info "停止服务 [$name]"

    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            kill -TERM "$pid"
            sleep 2
            if kill -0 "$pid" 2>/dev/null; then
                kill -KILL "$pid"
            fi
        fi
        rm -f "$pid_file"
    fi

    # 清理监控文件
    rm -f "$monitor_file"

    log_info "$name 已停止"
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."
    
    # 检查内存
    local mem_total=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    local mem_used=$(free -m | awk 'NR==2{printf "%.0f", $3}')
    local mem_usage=$((mem_used * 100 / mem_total))
    
    log_info "内存使用情况: ${mem_used}MB/${mem_total}MB (${mem_usage}%)"
    
    # 检查CPU核心数
    local cpu_cores=$(nproc)
    log_info "CPU核心数: $cpu_cores"
    
    # 检查负载
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    log_info "系统负载: $load_avg"
    
    # 检查进程数
    local process_count=$(ps aux | wc -l)
    log_info "当前进程数: $process_count"
    
    # 检查文件描述符限制
    local fd_limit=$(ulimit -n)
    log_info "文件描述符限制: $fd_limit"
}

# 显示服务状态
show_status() {
    log_info "多进程+多线程服务状态概览:"
    echo "----------------------------------------"
    printf "%-30s %-10s %-8s %-10s\n" "服务名称" "状态" "PID" "端口"
    echo "----------------------------------------"
    
    # 检查统计服务
    local pid=$(lsof -t -i:7701 2>/dev/null | head -1)
    if [ -n "$pid" ]; then
        printf "%-30s ${GREEN}%-10s${NC} %-8s %-10s\n" "Analytics_7701" "运行中" "$pid" "7701"
    else
        printf "%-30s ${RED}%-10s${NC} %-8s %-10s\n" "Analytics_7701" "已停止" "-" "7701"
    fi
    
    # 检查后台服务（多进程）
    for ((i=0; i<$BACKEND_PROCESSES; i++)); do
        local port=$((BACKEND_BASE_PORT + i))
        local service_name="Backend_${port}"
        local pid=$(lsof -t -i:$port 2>/dev/null | head -1)
        if [ -n "$pid" ]; then
            printf "%-30s ${GREEN}%-10s${NC} %-8s %-10s\n" "$service_name" "运行中" "$pid" "$port"
        else
            printf "%-30s ${RED}%-10s${NC} %-8s %-10s\n" "$service_name" "已停止" "-" "$port"
        fi
    done
    
    # 检查战斗服务（多进程）
    for ((i=0; i<$FIGHT_PROCESSES; i++)); do
        local port=$((FIGHT_BASE_PORT + i))
        local service_name="Fight_${port}"
        local pid=$(lsof -t -i:$port 2>/dev/null | head -1)
        if [ -n "$pid" ]; then
            printf "%-30s ${GREEN}%-10s${NC} %-8s %-10s\n" "$service_name" "运行中" "$pid" "$port"
        else
            printf "%-30s ${RED}%-10s${NC} %-8s %-10s\n" "$service_name" "已停止" "-" "$port"
        fi
    done
    
    # 检查区服（多进程）
    for zone_id in "${!ZONE_PROCESS_PORTS[@]}"; do
        local ports_str="${ZONE_PROCESS_PORTS[$zone_id]}"
        IFS=',' read -ra ports <<< "$ports_str"
        
        for port in "${ports[@]}"; do
            local service_name="Zone_${zone_id}_${port}"
            local pid=$(lsof -t -i:$port 2>/dev/null | head -1)
            if [ -n "$pid" ]; then
                printf "%-30s ${GREEN}%-10s${NC} %-8s %-10s\n" "$service_name" "运行中" "$pid" "$port"
            else
                printf "%-30s ${RED}%-10s${NC} %-8s %-10s\n" "$service_name" "已停止" "-" "$port"
            fi
        done
    done
    
    # 检查备份服务
    local pid=$(pgrep -f "backup_start_sg3.py" 2>/dev/null | head -1)
    if [ -n "$pid" ]; then
        printf "%-30s ${GREEN}%-10s${NC} %-8s %-10s\n" "Backup_SG3" "运行中" "$pid" "0"
    else
        printf "%-30s ${RED}%-10s${NC} %-8s %-10s\n" "Backup_SG3" "已停止" "-" "0"
    fi
    
    echo "----------------------------------------"
    
    # 显示进程统计
    local total_processes=$(ps aux | grep -E "(python|node)" | grep -v grep | wc -l)
    log_info "游戏相关进程总数: $total_processes"
}
