package sg.view.hero
{
	import laya.display.Node;
	import laya.events.Event;
	import laya.html.dom.HTMLDivElement;
	import laya.ui.Box;
	import laya.ui.Image;
	import laya.ui.Label;
	import sg.cfg.ConfigApp;
	import sg.cfg.ConfigServer;
	import sg.fight.logic.cfg.ConfigFight;
	import sg.fight.logic.utils.PassiveStrUtils;
	import sg.manager.ColorManager;
	import sg.manager.EffectManager;
	import sg.manager.ModelManager;
	import sg.map.utils.TestUtils;
	import sg.model.ModelAwaken;
	import sg.model.ModelHeroPure;
	import sg.model.ModelTalent;
	import ui.hero.heroTalentInfoUI;
    import sg.utils.Tools;
    import sg.model.ModelHero;
	import sg.utils.StringUtil;
	import sg.utils.MusicManager;

	/**
	 * 英雄天赋
	 * <AUTHOR>
	 */
    public class ViewHeroTalentInfo extends heroTalentInfoUI
    {
		private var modelHP:ModelHeroPure;
		
		private var mModelTalent:ModelTalent;
		private var mModelAwaken:ModelAwaken;
		
		
		private var boxFate:Box;
		private var widthBase:Number;
		
        public function ViewHeroTalentInfo()
        {
			//return;
			//this.hTalent.style.fontSize = 20;
			//this.hTalent.style.leading = 8;
			//this.hTalent.style.color = '#FFFFFF';
			//this.boxTalent.y = 0;
            this.tInfoName.text = Tools.getMsgById("_hero23");
			
			this.mBg.alpha = 0;
			
			if (ConfigApp.isHorizontal){
				this.widthBase = 800;
				this.mBox.width = this.widthBase;
				var ww:int = this.widthBase-40;
				this.boxTalent.width = this.boxAwakenInfo.width = ww;
				this.tLegend1.width = this.tLegend2.width = this.tLegend3.width = this.tLegend4.width = ww;
				this.tInfo.width = ww;
			}
			else{
				this.widthBase = this.mBox.width;
			}
			
			var fontSizeArr:Array = [EffectManager.getFontSize(2), EffectManager.getFontSize(4), EffectManager.getFontSize(6), EffectManager.getFontSize(8)];
			this.tTalent.fontSize =	this.tAwaken.fontSize = this.tLegend.fontSize = this.tInfoName.fontSize = fontSizeArr[3];
			//this.tAwaken.fontSize = fontSizeArr[3];
			//this.tLegend.fontSize = fontSizeArr[3];
			//this.tInfoName.fontSize = fontSizeArr[3];
			
			//this.tLegend1.fontSize = fontSizeArr[2];
			//this.tLegend2.fontSize = fontSizeArr[2];
			//this.tLegend3.fontSize = fontSizeArr[2];
			//this.tLegend4.fontSize = fontSizeArr[1];
			this.tLegend1.fontSize = this.tLegend2.fontSize = this.tLegend3.fontSize = fontSizeArr[2];
			this.tLegend4.fontSize = fontSizeArr[1];
			
			this.tLegend1.leading = fontSizeArr[2] * 0.3;
			this.tLegend4.leading = fontSizeArr[1] * 0.3;
			this.tInfo.fontSize = fontSizeArr[2];
			
			
			this.tInfoName.color = ColorManager.getUIColor('storyTitle');
			this.tInfo.color = ColorManager.getUIColor('storyInfo');
			//this.tLegend4.height = 70;
			
			if (ConfigApp.testFightType || TestUtils.isTestShow){
				this.on(Event.RIGHT_CLICK, this, this.closeSelf);
			}
        }
		
        override public function initData():void{
			//trace('ViewHeroTalentInfo   initData', this);
			//return;
			this.modelHP = this.currArg as ModelHeroPure;
			
			var talentKey:String = ModelTalent.getTalentKeyByData(this.modelHP.data);
			this.mModelTalent = ModelTalent.getModel(talentKey);
			this.mModelAwaken = ModelAwaken.getModel(talentKey);
			
			var titleStr:String = this.modelHP.getName(true);
			if (TestUtils.isTestShow){
				titleStr += ModelHero.getOpenTime(this.modelHP.id);
			}
			this.tName.text = titleStr;
			if(ConfigApp.isLandscape) {
				this.img_name.width = Math.max(300, this.tName.textField.textWidth + (300 -  50));
			} else {
				Tools.textLayout2(this.tName,this.img_name,300,160);
			}
			var info:String;
			var color:String;
			var sumY:Number = 70;
			if (this.mModelTalent && !this.modelHP.isMonster){
				//有天赋
				this.tTalent.visible = true;
				this.boxTalent.visible = true;
				//this.imgLine.visible = true;
				
				
				this.tTalent.y = sumY;
				info = this.mModelTalent.getName();
				this.tTalent.text = Tools.getMsgById("_hero32", [info]);
				sumY += this.tTalent.height + this.tTalent.fontSize0*0.7;
				
				sumY += this.initTalentBox(sumY);
				//this.hTalent.y = sumY;
				//info = this.mModelTalent.getInfoHtml();
				//info = StringUtil.substituteWithLineAndColor(info, "#FCAA44", "#ffffff");
				//this.hTalent.innerHTML = info;
				//sumY += this.hTalent.contextHeight + 20;

				//如果是传奇天赋，特殊显示
				var arr:Array = ConfigFight.legendTalent[this.modelHP.id]?ConfigFight.legendTalent[this.modelHP.id]:ConfigFight.legendTalentFight[this.modelHP.id];
				if (arr){
					this.boxLegend.y = sumY;
					this.boxLegend.visible = true;
					this.initLegendBox();
					sumY += this.boxLegend.height;
				}
				else
				{
					this.boxLegend.visible = false;
				}
				//sumY += 5;
			}
			else{
				//无天赋
				this.tTalent.visible = false;
				this.boxTalent.visible = false;
				this.boxLegend.visible = false;
				//this.imgLine.visible = false;
			}
			//觉醒天赋
			sumY += this.initAwakenBox(sumY);
			
			if (this.modelHP.hasStory){
				this.imgLine.visible = this.tInfo.visible = this.tInfoName.visible = true;
				//划线
				this.imgLine.y = sumY;
				sumY += 20;
				//传记说明
				this.tInfoName.y = sumY;
				sumY += this.tInfoName.height + this.tInfoName.fontSize0*0.7;
				this.tInfo.y = sumY;
				info = Tools.getMsgById(this.modelHP.cfg.info);
				this.tInfo.text = info;
				this.tInfo.height = this.tInfo.textField.textHeight;
				sumY += this.tInfo.height + this.tInfo.fontSize0 * 0.7;
			}
			else{
				this.imgLine.visible = this.tInfo.visible = this.tInfoName.visible = false;
			}
			
			//显示宿命
			sumY += this.initFateBox(sumY);
			//末尾
			sumY += 10;
			
			var scaleValue:Number = 1;
			var showHeight:Number = Laya.stage.height - 105 *2;
			if (showHeight < sumY) {
				this.mBox.centerY = 0;
				if (Laya.stage.height < sumY){
					if (this['boxAll']){
						scaleValue = Laya.stage.height / sumY;
					}
				}
				
			} else {
				var centerY:Number = (showHeight - sumY) * (-0.5);
				centerY = centerY < -100 ? -100 : centerY;
				this.mBox.centerY = centerY;
			}
			//总和高度
			if (this['boxAll']){
				this['boxAll'].scale(scaleValue, scaleValue);
			}
			this.mBox.height = sumY * scaleValue;
			this.mBox.width = this.widthBase * scaleValue;
			//音效
			MusicManager.playSoundHero(this.modelHP.id);
        }   
		/**
		 * 得到基础box
		 */
		public function get boxBase():Box{
			if (this['boxAll']){
				return this['boxAll'];
			}
			return this.mBox;
		}
		
		/**
		 * 基础天赋
		 */
		public function initTalentBox(yy:int):int{
			this.boxTalent.destroyChildren();
			this.boxTalent.y = yy;

			this.tTalent.color = ColorManager.getUIColor('talentTitle');;
			var color0:String = ColorManager.getUIColor('talentInfo1');
			var color1:String = ColorManager.getUIColor('talentInfo2');
			var color2:String = ColorManager.getUIColor('talentSubInfo1');
			var color3:String = ColorManager.getUIColor('talentSubInfo2');
			var sign:String = Tools.getMsgById('_hero33');
			var info:String = this.mModelTalent.getInfoHtml(this.modelHP.getExplain());
			var infoArr:Array = info.split('；');
			var len:int = infoArr.length;
			var sumY:Number = 0;
			var tempY:Number = 0;
			
			for (var i:int = 0; i < len; i++) 
			{
				info = infoArr[i];
				if (!info)
					continue;
					
				var html:HTMLDivElement = new HTMLDivElement;
				html.width = this.boxTalent.width;
				
				if (info.substr(0, 1) == '_'){
					//属于子项目
					info = info.substr(1);
					info = StringUtil.repeat('&nbsp;', 4) + StringUtil.substituteWithColor(info, color2, color3);
					html.style.fontSize = this.tLegend2.fontSize0;
				}
				else{
					info = StringUtil.substituteWithColor(sign + info, color2, (i % 2 == 0)?color0:color1);
					
					html.style.fontSize = this.tLegend1.fontSize0;
				}
				tempY = html.style.fontSize0*0.5;
				//html.style.fontSize = 20;
				html.style.leading = html.style.fontSize0*0.3;

				html.innerHTML = info;
				html.y = sumY;
				this.boxTalent.addChild(html);
				
				sumY += html.contextHeight + tempY;
			}
			if (len)
				sumY -= tempY;
			this.boxTalent.height = sumY;
			return sumY;
		}
		

		/**
		 * 传奇属性
		 */
		public function initLegendBox():void{
			//var mh:ModelHero = ModelManager.instance.modelGame.getModelHero(this.mModelTalent.id);
			//var starLv:int = mh.getStar();
			var starLv:int = this.modelHP.getStar();
			//这里不需要包含转生的星级
			var starLvMax:int = ModelHero.getStarMax(modelHP.id, -1);
			var sumY:Number = 25;
			
			this.tLegend.color = ColorManager.getUIColor('legendTalentTitle');
			this.tLegend1.color = ColorManager.getUIColor('legendTalentInfo');
			this.tLegend2.color = ColorManager.getUIColor('legendTalentNext');
			this.tLegend3.color = ColorManager.getUIColor('legendTalentMax');
			this.tLegend4.color = ColorManager.getUIColor('legendTalentHelp');
			
			var info:String = Tools.getMsgById(this.mModelTalent.getLegendTalent(), [this.mModelTalent.getLegendValue(starLv)]);
			this.tLegend.text = Tools.getMsgById('_hero31');

			this.tLegend.y = sumY;
			//this.tLegend.textField.typeset();
			this.tLegend.height = this.tLegend.textField.textHeight;
			sumY += this.tLegend.height + this.tLegend.fontSize0*0.7;
			
			this.tLegend1.text = info;
			this.tLegend1.y = sumY;
			this.tLegend1.height = this.tLegend1.textField.textHeight;
			sumY += this.tLegend1.height + this.tLegend1.fontSize0*0.3;
			
			if (starLv < starLvMax){
				this.tLegend2.visible = true;
				this.tLegend2.text = Tools.getMsgById('_hero34', [this.mModelTalent.getLegendValue(starLv + 1)]);
				this.tLegend2.y = sumY;
				this.tLegend2.height = this.tLegend2.textField.textHeight;
				sumY += this.tLegend2.height + this.tLegend2.fontSize0*0.3;
				this.tLegend3.text = Tools.getMsgById('_hero35', [this.mModelTalent.getLegendValue(starLvMax)]);
			}
			else{
				this.tLegend2.visible = false;
				this.tLegend3.text = Tools.getMsgById('skill_lv_max');
			}
			
			
			this.tLegend3.y = sumY;
			this.tLegend3.height = this.tLegend3.textField.textHeight;
			sumY += this.tLegend3.height + this.tLegend3.fontSize0*0.7;
			
			this.tLegend4.text = Tools.getMsgById('_hero36');
			this.tLegend4.y = sumY;
			this.tLegend4.height = this.tLegend4.textField.textHeight;
			sumY += this.tLegend4.height;
			
			this.boxLegend.height = sumY;
		}
		
		/**
		 * 觉醒天赋
		 */
		public function initAwakenBox(yy:int):int{
			this.boxAwaken.visible = false;
			this.boxAwakenInfo.destroyChildren();
			var info:String = '';
			var revivedInfo:String;
			var titleType:int;
			var titleInfo:String;
			
			
			if (this.modelHP.isMonster){
				//不是正经阵法，不可能被玩家获得，如果有传记则显示传记，但无标题
				info = this.modelHP.getMonsterTalentInfo();
				//info = Tools.getMsgById(this.modelHP.cfg.info);
				if (info){
					titleType = 4;
				}
				else{
					titleType = 0;
				}
			}
			else{
				titleType = 0;
				if (this.modelHP.awaken){
					if (this.mModelAwaken){
						//有觉醒天赋
						info = this.mModelAwaken.getInfoHtml(this.modelHP.getExplain());
						titleType += 1;
					}
				}
				if (this.modelHP.revived){
					//有转生天赋
					var talentKey:String = ModelTalent.getTalentKeyByData(this.modelHP.data);
					if (ModelTalent.hasRevived(talentKey)){
						revivedInfo = ModelTalent.getRevivedInfo(talentKey, 1, this.modelHP.getExplain());
						info = '~' + revivedInfo+ '；' + info;
						titleType += 2;
					}
				}
			}

			
			if (titleType){
				this.boxAwaken.visible = true;
				var node:Node = this.boxAwaken.getChildAt(0);
				if (node is Image){
					var lineImage:Image = node as Image;
					//线
					if (this.modelHP.isMonster){
						lineImage.visible = false;
						this.tAwaken.y = 5;
					}
					else{
						lineImage.visible = true;
						this.tAwaken.y = 25;
					}
				}

				if (titleType == 1){
					titleInfo = Tools.getMsgById('_hero38');
				}
				else if (titleType == 2){
					titleInfo = Tools.getMsgById('_hero55');
				}
				else if (titleType == 3){
					titleInfo = Tools.getMsgById('_hero58');
				}	
				else if (titleType == 4){
					titleInfo = Tools.getMsgById('_hero41',[this.modelHP.getName()]);
				}
				else{
					titleInfo = '';
				}
				this.tAwaken.text = titleInfo;
				this.tAwaken.color = ColorManager.getUIColor('talentAwakenTitle');
					
				var color0:String = ColorManager.getUIColor('talentAwakenInfo');
				//var color1:String = '#EEDDAA';
				var color2:String = ColorManager.getUIColor('talentSubInfo1');
				var color3:String = ColorManager.getUIColor('talentSubInfo2');
				var colorRevived:String = ColorManager.getUIColor('talentRevivedInfo');
				var sign:String = Tools.getMsgById('_hero33');

				var infoArr:Array = info.split('；');
				var len:int = infoArr.length;
				var sumY:Number = 0;
				var tempY:Number = 0;
					
				for (var i:int = 0; i < len; i++) 
				{
					info = infoArr[i];
					if (!info)
						continue;
						
					var html:HTMLDivElement = new HTMLDivElement;
					html.width = this.boxAwakenInfo.width;
					
					if (info.substr(0, 1) == '_'){
						//属于子项目
						info = info.substr(1);
						info = StringUtil.repeat('&nbsp;', 4) + StringUtil.substituteWithColor(info, color2, color3);
						html.style.fontSize = this.tLegend2.fontSize0;
					}
					else if (info.substr(0, 1) == '~'){
						//属于转生天赋
						info = info.substr(1);
						info = StringUtil.substituteWithColor(sign + info, colorRevived, colorRevived);
						html.style.fontSize = this.tLegend1.fontSize0;
					}
					else{
						info = StringUtil.substituteWithColor(sign + info, color2, color0);
						html.style.fontSize = this.tLegend1.fontSize0;
					}
					tempY = html.style.fontSize0 * 0.5;
					//html.style.fontSize = 20;
					html.style.leading = html.style.fontSize0*0.3;

					html.innerHTML = info;
					html.y = sumY;
					this.boxAwakenInfo.addChild(html);
					
					sumY += html.contextHeight + tempY;
				}
				if (len)
					sumY -= tempY;
				
				this.boxAwakenInfo.y = this.tAwaken.y + this.tAwaken.textField.textHeight + this.tLegend.fontSize0 * 0.7;
				this.boxAwakenInfo.height = sumY;
				
				this.boxAwaken.visible = true;
				this.boxAwaken.y = yy;

				sumY = sumY + this.boxAwakenInfo.y;
				this.boxAwaken.height = sumY;
				//this.boxAwaken.height = sumY + this.boxAwakenInfo.y;
				
								
				

				
				return sumY;
		
			}
			return 0;
		}
		
		/**
		 * 宿命
		 */
		public function initFateBox(initY:int):int{
			if (ConfigFight.banHeroFate)
				return 0;
			
			//if (!ConfigApp.testFightType)
				//return 0;
			if(!this.boxFate){
				this.boxFate = new Box();
				this.boxBase.addChild(this.boxFate);
			}
			this.boxFate.destroyChildren();
			this.boxFate.y = initY;
			
			//if (!ConfigApp.testFightType && this.mModelHero.isOther)
				//return 0;
			
			var cfgArr:Array = ModelHero.getCfgFateArr(this.modelHP.id);
			var getArr:Array = this.modelHP.fate;
			var len:int = cfgArr.length;
			if (len == 0){
				return 0;
			}
			cfgArr.sort(function(a:int, b:int):int { return a - b; });
			
			//宿命标题
			var sumY:Number = 0;
			var label:Label = new Label(Tools.getMsgById('_public4'));
			label.y = sumY;
			label.x = this.tInfo.x;
			label.width = this.tInfo.width;
			label.fontSize = this.tLegend.fontSize0;
			label.color = ColorManager.getUIColor('fateTitle');  //'#66CC66';
			this.boxFate.addChild(label);
			sumY += label.height + label.fontSize0*0.7;
			
			var html:HTMLDivElement;
			var info:String;
			var id:String;
			var fateCfg:Object;
			var heroCfg:Object = this.modelHP.cfg;
			var fateHeroId:String;
			var fateHeroCfg:Object;
			for (var i:int = 0; i < len; i++) 
			{
				id = cfgArr[i];
				fateCfg = ConfigServer.fate[id];
				if (fateCfg){
					info = Tools.getMsgById(fateCfg.name);
					if (fateCfg.call) {
						info += '('+Tools.getMsgById('_hero17')+')';
					}
					else{
						fateHeroId = heroCfg.fate[id][1];
						fateHeroCfg = ConfigServer.hero[fateHeroId];
						if(fateHeroCfg){
							info += '(' + Tools.getMsgById(fateHeroCfg.name) + ')';
						}
						else{
							info += '(' + fateHeroId + ')';
						}
					}
					
					var passiveObj:* = fateCfg.passive;
					if (passiveObj){
						info = '['+info+']：'+PassiveStrUtils.translatePassiveInfo(passiveObj, true, false, 1);
					}
					
					//id = 'fate_info' + id.substr(4, 4);
					//if (Tools.hasMsgById(id)){
						//info += ':'+Tools.getMsgById(id);
					//}
					//else{
						//info += ':'+Tools.getMsgById(id);
					//}
//
					html = new HTMLDivElement;
					html.y = sumY;
					html.x = this.tInfo.x;
					html.width = this.tInfo.width;
					html.style.fontSize = this.tLegend4.fontSize0;
					html.style.leading = html.style.fontSize0*0.3;
					html.style.wordWrap = true;
					//html.color = '#99FF99';
					
					html.innerHTML = StringUtil.substituteWithColor(info, ColorManager.getUIColor('fateNum'), ColorManager.getUIColor('fateInfo'));
					if (!getArr || getArr.indexOf(id) == -1){
						html.alpha = 0.3;
					}
					
					this.boxFate.addChild(html);
					sumY += html.contextHeight;
				}
				sumY += this.tLegend4.fontSize0*0.3;
			}
			return sumY;
		}
    }
}