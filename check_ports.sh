#!/bin/bash

# 端口检查工具
# 用于诊断端口占用问题

# 游戏服务器端口列表
GAME_PORTS=(7701 8500 3001 5001 5004 5005 5006)

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "游戏服务器端口检查工具"
echo "========================"

check_port() {
    local port=$1
    local service_name=$2
    
    # 检查端口是否被占用
    local pid=$(lsof -t -i:$port 2>/dev/null)
    
    if [ -n "$pid" ]; then
        # 获取进程信息
        local process_info=$(ps -p $pid -o pid,ppid,cmd --no-headers 2>/dev/null)
        local process_name=$(ps -p $pid -o comm --no-headers 2>/dev/null)
        
        echo -e "${RED}端口 $port ($service_name): 被占用${NC}"
        echo "  PID: $pid"
        echo "  进程: $process_name"
        echo "  命令: $process_info"
        echo "  清理命令: kill -KILL $pid"
        echo ""
        return 1
    else
        echo -e "${GREEN}端口 $port ($service_name): 空闲${NC}"
        return 0
    fi
}

force_clean_port() {
    local port=$1
    local pid=$(lsof -t -i:$port 2>/dev/null)
    
    if [ -n "$pid" ]; then
        echo "强制清理端口 $port 上的进程 $pid..."
        kill -KILL $pid 2>/dev/null
        sleep 1
        
        # 再次检查
        local new_pid=$(lsof -t -i:$port 2>/dev/null)
        if [ -z "$new_pid" ]; then
            echo -e "${GREEN}端口 $port 已释放${NC}"
        else
            echo -e "${RED}端口 $port 清理失败${NC}"
        fi
    else
        echo -e "${GREEN}端口 $port 已经空闲${NC}"
    fi
}

# 主程序
ACTION="$1"

case "$ACTION" in
    "check"|"")
        echo "检查游戏服务器端口状态..."
        echo ""
        
        occupied_ports=0
        
        check_port 7701 "Analytics" || occupied_ports=$((occupied_ports + 1))
        check_port 8500 "Backend" || occupied_ports=$((occupied_ports + 1))
        check_port 3001 "Fight" || occupied_ports=$((occupied_ports + 1))
        check_port 5001 "Zone1" || occupied_ports=$((occupied_ports + 1))
        check_port 5004 "Zone4" || occupied_ports=$((occupied_ports + 1))
        check_port 5005 "Zone5" || occupied_ports=$((occupied_ports + 1))
        check_port 5006 "Zone6" || occupied_ports=$((occupied_ports + 1))
        
        echo "========================"
        if [ $occupied_ports -eq 0 ]; then
            echo -e "${GREEN}所有端口都空闲，可以安全启动服务${NC}"
        else
            echo -e "${YELLOW}发现 $occupied_ports 个端口被占用${NC}"
            echo "建议运行: $0 clean 清理所有占用的端口"
        fi
        ;;
        
    "clean")
        echo "强制清理所有游戏服务器端口..."
        echo ""
        
        for port in "${GAME_PORTS[@]}"; do
            force_clean_port $port
        done
        
        echo ""
        echo "清理完成，等待2秒后重新检查..."
        sleep 2
        
        echo ""
        $0 check
        ;;
        
    "clean-port")
        PORT="$2"
        if [ -z "$PORT" ]; then
            echo "用法: $0 clean-port <端口号>"
            echo "例如: $0 clean-port 5006"
            exit 1
        fi
        
        echo "清理端口 $PORT..."
        force_clean_port $PORT
        ;;
        
    "monitor")
        echo "实时监控端口状态 (按Ctrl+C退出)..."
        echo ""
        
        while true; do
            clear
            echo "游戏服务器端口实时监控 - $(date)"
            echo "========================"
            
            for port in "${GAME_PORTS[@]}"; do
                case $port in
                    7701) service="Analytics" ;;
                    8500) service="Backend" ;;
                    3001) service="Fight" ;;
                    5001) service="Zone1" ;;
                    5004) service="Zone4" ;;
                    5005) service="Zone5" ;;
                    5006) service="Zone6" ;;
                esac
                
                check_port $port $service > /dev/null
                if [ $? -eq 0 ]; then
                    echo -e "${GREEN}$port ($service): 空闲${NC}"
                else
                    local pid=$(lsof -t -i:$port 2>/dev/null)
                    echo -e "${RED}$port ($service): 占用 (PID: $pid)${NC}"
                fi
            done
            
            echo ""
            echo "按Ctrl+C退出监控"
            sleep 3
        done
        ;;
        
    *)
        echo "端口检查工具"
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  check           - 检查所有游戏端口状态 (默认)"
        echo "  clean           - 强制清理所有占用的端口"
        echo "  clean-port <端口> - 清理指定端口"
        echo "  monitor         - 实时监控端口状态"
        echo ""
        echo "游戏服务器端口:"
        echo "  7701 - Analytics (统计服务)"
        echo "  8500 - Backend (后台服务)"
        echo "  3001 - Fight (战斗服务)"
        echo "  5001 - Zone1 (1区服务)"
        echo "  5004 - Zone4 (4区服务)"
        echo "  5005 - Zone5 (5区服务)"
        echo "  5006 - Zone6 (6区服务)"
        echo ""
        echo "示例:"
        echo "  $0 check          # 检查端口状态"
        echo "  $0 clean          # 清理所有端口"
        echo "  $0 clean-port 5006 # 只清理6区端口"
        echo "  $0 monitor        # 实时监控"
        exit 1
        ;;
esac
