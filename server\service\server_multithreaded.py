# -*- coding: utf-8 -*-
"""
多线程优化版本的游戏服务器
将原有的单线程处理改为多线程处理，提高并发能力
"""

import os, sys, time, datetime, random, json, struct
from functools import partial
from tornado import web, websocket, gen, ioloop, autoreload, httpserver
from tornado.options import parse_command_line, options
from tornado.log import app_log
import cPickle as pickle
from concurrent.futures import ThreadPoolExecutor
from tornado.concurrent import run_on_executor
import threading
import queue
import copy

# 导入原有模块
sys.path.insert(0, os.path.dirname(__file__))
from server import *  # 导入原有的所有类和函数

class MultiThreadedAppSocket(websocket.WebSocketHandler):
    """
    多线程版本的WebSocket处理器
    将业务逻辑处理放到线程池中执行，避免阻塞主事件循环
    """
    
    # 配置线程池 - 根据8核16G服务器优化
    executor = ThreadPoolExecutor(max_workers=100)  # 100个工作线程
    
    # 继承原有的类变量
    user_socket_dict = {}
    service_maintain = {
        'full_maintain': 1,
        'zone_maintain': 1,
        'white_list': [],
        'info_msg': {
            'maintain_msg': 'service_info',
            'future_msg': 'service_future'
        }
    }
    black_user = {}
    req_times = {}
    
    def open(self):
        """WebSocket连接打开"""
        self.set_nodelay(True)
        self.uid = None
        self.close_by_me = False
        app_log.info("MultiThreaded WebSocket opened")

    def check_origin(self, origin):
        """允许跨域连接"""
        return True

    @gen.coroutine
    def on_message(self, data):
        """
        消息处理入口 - 异步调用线程池处理业务逻辑
        """
        start_time = time.time()
        
        try:
            # 解析基本数据
            data_dict = json.loads(data)
            pid = data_dict['pid']
            phone_id = data_dict['phone_id']
            method = data_dict['method']
            params = data_dict['params']
            guide = data_dict.get('guide', None)
            
            # 在线程池中处理业务逻辑
            result = yield self.process_message_in_thread(
                pid, phone_id, method, params, guide, start_time
            )
            
            # 发送响应
            if result:
                try:
                    return_json_res = json.dumps(result, default=json_default)
                    self.write_message(return_json_res)
                    r_len = len(return_json_res)
                except Exception as e:
                    app_log.error('Error sending response: %s', e, exc_info=True)
                    r_len = 0
                
                # 记录日志
                total_time = int((time.time() - start_time) * 1000)
                app_log.info('MultiThreaded Message: %s, %s, %s, %s, %s, %s, %s, %s', 
                           total_time, r_len, self.uid, method, 
                           self.request.remote_ip, options.zone, params, guide)
            
        except Exception as e:
            app_log.error('Error in on_message: %s', e, exc_info=True)
            # 发送错误响应
            error_response = {
                'method': 'error',
                'code': 500,
                'pid': 0,
                'data': {'msg': 'Server error'},
                'time': datetime.datetime.now()
            }
            try:
                self.write_message(json.dumps(error_response, default=json_default))
            except:
                pass

    @run_on_executor
    def process_message_in_thread(self, pid, phone_id, method, params, guide, start_time):
        """
        在线程池中处理业务逻辑
        这个方法会在独立的线程中执行，不会阻塞主事件循环
        """
        try:
            data = None
            code = 0
            now = datetime.datetime.now()
            
            # 检查用户黑名单
            if method == 'login':
                uid = params.get('uid')
            else:
                uid = self.uid
                
            if uid and uid in self.black_user:
                if self.black_user[uid] > start_time:
                    return None  # 关闭连接
            
            # 检查服务维护状态
            service_maintain = self.service_maintain
            if_service = False
            
            if (service_maintain['full_maintain'] == 0 or 
                service_maintain['zone_maintain'] == 0 or 
                now < game_config.zone[options.zone][2]):
                
                user_ip = self.request.remote_ip
                if not uid:
                    uid = ''
                    old_uid = ''
                else:
                    if game_config.zone[options.zone][8]:
                        old_uid = uid % settings.UIDADD
                    else:
                        old_uid = uid
                        
                if (user_ip not in service_maintain['white_list'] and 
                    str(uid) not in service_maintain['white_list'] and 
                    str(old_uid) not in service_maintain['white_list']):
                    if_service = True
            
            if if_service:
                if service_maintain['full_maintain'] == 0 or service_maintain['zone_maintain'] == 0:
                    service_msg = service_maintain['info_msg']['maintain_msg']
                else:
                    service_msg = service_maintain['info_msg']['future_msg']
                data = {'msg': service_msg}
                code = 2000
                
            elif method == 'login':
                # 处理登录逻辑
                data, code = self.process_login_in_thread(params, now)
                
            else:
                # 处理其他业务逻辑
                if self.uid:
                    data, code = self.process_business_logic_in_thread(
                        method, params, phone_id, now, guide
                    )
                else:
                    return None  # 关闭连接
            
            # 处理接口防刷
            self.handle_anti_spam(start_time, method)
            
            return {
                'method': method,
                'code': code,
                'pid': pid,
                'data': data,
                'time': datetime.datetime.now()
            }
            
        except Exception as e:
            app_log.error('Error in process_message_in_thread: %s', e, exc_info=True)
            return {
                'method': method,
                'code': 500,
                'pid': pid,
                'data': {'msg': str(e)},
                'time': datetime.datetime.now()
            }

    def process_login_in_thread(self, params, now):
        """在线程中处理登录逻辑"""
        try:
            # 这里需要调用原有的登录逻辑，但要注意线程安全
            # 由于原有代码使用了yield，我们需要同步版本
            # 这是一个简化版本，实际需要根据具体业务逻辑调整
            
            # 模拟登录处理
            uid = params.get('uid')
            if uid and uid in User.users:
                user = User.users[uid]
                self.uid = uid
                
                # 检查冻结状态
                freeze_list = user.records['freeze']
                if freeze_list[0] == 1 and freeze_list[1] > now:
                    return {'msg': user.get_return_msg('freeze_user')}, 18888
                
                # 登录成功，返回用户数据
                data = {
                    'uid': uid,
                    'records': user.records,
                    'world_lv': world.world_lv if world else 1,
                    'online_time': user.get_online_time() if hasattr(user, 'get_online_time') else 0
                }
                return data, 0
            else:
                return {'msg': 'User not found'}, 10309
                
        except Exception as e:
            app_log.error('Error in login: %s', e, exc_info=True)
            return {'msg': str(e)}, 500

    def process_business_logic_in_thread(self, method, params, phone_id, now, guide):
        """在线程中处理业务逻辑"""
        try:
            if self.uid not in User.users:
                return {'msg': 'User not found'}, 10309
            
            # 获取用户对象的副本以避免线程安全问题
            user = copy.deepcopy(User.users[self.uid])
            user.now = now
            user.phone_id = phone_id
            
            # 检查冻结状态
            freeze_list = user.records['freeze']
            if freeze_list[0] == 1 and freeze_list[1] > now:
                return None, 0  # 关闭连接
            
            # 处理业务方法
            is_w = False
            if method.startswith('w.'):
                # 世界相关方法
                if world and hasattr(world, method[2:]):
                    m = getattr(world, method[2:])
                    is_w = True
                    data = m(user, params)
                else:
                    return {'msg': 'Method not found'}, 404
            else:
                # 用户相关方法
                if hasattr(user, method):
                    m = getattr(user, method)
                    data = m(params)
                else:
                    return {'msg': 'Method not found'}, 404
            
            # 处理引导
            if guide and hasattr(user, 'do_guide'):
                user.do_guide(guide)
            
            return data, 0
            
        except Model_Error as e:
            return {'msg': e.msg}, e.code
        except Exception as e:
            app_log.error('Error in business logic: %s', e, exc_info=True)
            return {'msg': str(e)}, 500

    def handle_anti_spam(self, start_time, method):
        """处理接口防刷"""
        if not self.uid or not game_config.help_msg.get('api_malice_req_switch'):
            return
            
        now_time = int(start_time)
        duration = game_config.help_msg['api_malice_req_duration']
        start_t = now_time - (duration + 1)
        stop_t = now_time - 1
        limit = game_config.help_msg['api_malice_req_limit']
        
        # 使用线程锁保护共享数据
        with threading.Lock():
            self.req_times.setdefault(self.uid, {})
            
            duration_second_unsafe = []
            for i in range(start_t, stop_t):
                if len(self.req_times[self.uid].get(str(i), [])) <= limit:
                    duration_second_unsafe.append(False)
                else:
                    duration_second_unsafe.append(True)
                    
            if all(duration_second_unsafe):
                self.black_user[self.uid] = now_time + game_config.help_msg.get('api_malice_freeze_seconds', 3600)
            else:
                # 删除超时数据
                for k in list(self.req_times[self.uid].keys()):
                    if int(k) < start_t:
                        del self.req_times[self.uid][k]
                        
                self.req_times[self.uid].setdefault(str(now_time), [])
                self.req_times[self.uid][str(now_time)].append(method)

    def on_close(self):
        """WebSocket连接关闭"""
        app_log.info("MultiThreaded WebSocket closed, %s", self.uid)
        
        if self.close_by_me:
            return
            
        if self.uid and self.uid in User.users:
            # 处理用户登出逻辑
            try:
                User.users[self.uid].on_logout()
            except:
                pass
                
        if self.uid and self.uid in self.req_times:
            del self.req_times[self.uid]


class MultiThreadedApi(web.RequestHandler):
    """
    多线程版本的API处理器
    """
    executor = ThreadPoolExecutor(max_workers=50)  # 50个API处理线程
    
    @gen.coroutine
    def post(self):
        """异步处理API请求"""
        start_time = time.time()
        
        try:
            result = yield self.process_api_in_thread()
            if result:
                self.finish(result)
        except Exception as e:
            app_log.error('Error in API: %s', e, exc_info=True)
            self.finish(pickle.dumps({'error': str(e)}, -1))
    
    @run_on_executor
    def process_api_in_thread(self):
        """在线程池中处理API逻辑"""
        try:
            pwd = self.get_argument('pwd')
            if settings.PWD != pwd:
                raise Exception('Error Request')
                
            method, params = pickle.loads(self.request.body)
            
            # 处理API方法
            if hasattr(self, f'handle_{method}'):
                handler = getattr(self, f'handle_{method}')
                result = handler(params)
            else:
                result = {'error': 'Unknown method'}
                
            return pickle.dumps(result, -1)
            
        except Exception as e:
            app_log.error('Error in API processing: %s', e, exc_info=True)
            return pickle.dumps({'error': str(e)}, -1)


def create_multithreaded_application():
    """创建多线程版本的应用"""
    return web.Application([
        (r"/gateway/", MultiThreadedAppSocket),
        (r"/api/", MultiThreadedApi),
        (r"/ip/", Ip),  # 保持原有的简单处理器
        (r"/mj/", Mj),  # 保持原有的简单处理器
    ])


def main_multithreaded():
    """多线程版本的主函数"""
    global world
    
    # 使用多线程版本的应用
    application = create_multithreaded_application()
    
    app_log.info('init_server start (multithreaded) >>>>>>')
    init_server_from_db()
    app_log.info('init_server end (multithreaded) >>>>>>')
    
    # 启动定时任务（保持在主线程）
    ioloop.PeriodicCallback(pk_yard_run, 1000*10).start()
    ioloop.PeriodicCallback(pk_yard_new_run, 1000*10).start()
    ioloop.PeriodicCallback(world_run, 1000).start()
    ioloop.PeriodicCallback(fight_run, 1000).start()
    ioloop.PeriodicCallback(troop_move_run, 1000).start()
    ioloop.PeriodicCallback(player_robot_run, 1000).start()
    ioloop.PeriodicCallback(city_state_run, 1000).start()
    
    # 创建HTTP服务器
    if settings.USE_SSL:
        # SSL配置保持不变
        if settings.WHERE in ['ea37', 'cb37kr']:
            certfile = os.path.join(os.path.abspath('keys'), '37games.com.crt')
            keyfile = os.path.join(os.path.abspath('keys'), '37games.com.key')
        else:
            certfile = os.path.join(os.path.abspath("keys"), "ptkill.com.crt")
            keyfile = os.path.join(os.path.abspath("keys"), "ptkill.com.key")
            
        server = httpserver.HTTPServer(application, ssl_options={
            "certfile": certfile,
            "keyfile": keyfile,
        })
    else:
        server = httpserver.HTTPServer(application)
    
    server.listen(game_config.zone[options.zone][1][1])
    
    app_log.info('Multithreaded server start done >>>>>>')
    ioloop.IOLoop.current().start()


if __name__ == '__main__':
    options.define('zone', type=int, default=1, help='server zone')
    parse_command_line()
    main_multithreaded()
    print('Multithreaded server start====================')
