#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
游戏服务器性能监控脚本
监控多进程+多线程服务器的性能指标
"""

import os
import sys
import time
import json
import psutil
import requests
import threading
from datetime import datetime
from collections import defaultdict, deque

class GameServerMonitor:
    def __init__(self):
        self.log_dir = "/data/server/logs"
        self.monitor_dir = "/data/server/monitor"
        self.pid_dir = "/data/server/pids"
        
        # 性能数据存储
        self.performance_data = defaultdict(lambda: deque(maxlen=100))
        self.alert_thresholds = {
            'cpu_usage': 80,      # CPU使用率阈值
            'memory_usage': 85,   # 内存使用率阈值
            'response_time': 1000, # 响应时间阈值(ms)
            'error_rate': 5,      # 错误率阈值(%)
            'connection_count': 1000  # 连接数阈值
        }
        
        # 服务端口配置
        self.service_ports = {
            'analytics': [7701],
            'backend': [8500, 8501, 8502],
            'fight': [3001, 3002, 3003],
            'zone1': [5001, 5011],
            'zone4': [5004, 5014],
            'zone5': [5005, 5015]
        }
        
        self.running = True
        
    def get_process_info(self, pid):
        """获取进程信息"""
        try:
            process = psutil.Process(pid)
            return {
                'pid': pid,
                'cpu_percent': process.cpu_percent(),
                'memory_percent': process.memory_percent(),
                'memory_info': process.memory_info(),
                'num_threads': process.num_threads(),
                'connections': len(process.connections()),
                'status': process.status(),
                'create_time': process.create_time()
            }
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return None
    
    def get_running_services(self):
        """获取正在运行的服务"""
        services = {}
        
        if not os.path.exists(self.pid_dir):
            return services
            
        for pid_file in os.listdir(self.pid_dir):
            if pid_file.endswith('.pid'):
                service_name = pid_file[:-4]
                pid_path = os.path.join(self.pid_dir, pid_file)
                
                try:
                    with open(pid_path, 'r') as f:
                        pid = int(f.read().strip())
                    
                    process_info = self.get_process_info(pid)
                    if process_info:
                        services[service_name] = process_info
                        
                except (ValueError, IOError):
                    continue
                    
        return services
    
    def check_port_health(self, port):
        """检查端口健康状态"""
        try:
            # 尝试连接端口
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            
            if result == 0:
                # 端口可连接，尝试HTTP健康检查
                try:
                    start_time = time.time()
                    response = requests.get(f'http://127.0.0.1:{port}/health', timeout=5)
                    response_time = (time.time() - start_time) * 1000
                    
                    return {
                        'status': 'healthy' if response.status_code == 200 else 'unhealthy',
                        'response_time': response_time,
                        'status_code': response.status_code
                    }
                except requests.RequestException:
                    return {
                        'status': 'port_open',
                        'response_time': None,
                        'status_code': None
                    }
            else:
                return {
                    'status': 'port_closed',
                    'response_time': None,
                    'status_code': None
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'response_time': None,
                'status_code': None
            }
    
    def get_system_metrics(self):
        """获取系统指标"""
        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'cpu_count': psutil.cpu_count(),
            'memory': psutil.virtual_memory()._asdict(),
            'disk': psutil.disk_usage('/')._asdict(),
            'network': psutil.net_io_counters()._asdict(),
            'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else None,
            'boot_time': psutil.boot_time(),
            'timestamp': time.time()
        }
    
    def analyze_log_errors(self, log_file, lines=100):
        """分析日志文件中的错误"""
        if not os.path.exists(log_file):
            return {'error_count': 0, 'errors': []}
            
        try:
            with open(log_file, 'r') as f:
                # 读取最后N行
                lines_list = deque(f, maxlen=lines)
                
            errors = []
            error_count = 0
            
            for line in lines_list:
                if any(keyword in line.lower() for keyword in ['error', 'exception', 'traceback', 'failed']):
                    error_count += 1
                    errors.append({
                        'timestamp': datetime.now().isoformat(),
                        'message': line.strip()
                    })
                    
            return {
                'error_count': error_count,
                'errors': errors[-10:]  # 只返回最近10个错误
            }
            
        except IOError:
            return {'error_count': 0, 'errors': []}
    
    def check_alerts(self, metrics):
        """检查告警条件"""
        alerts = []
        
        # CPU使用率告警
        if metrics['system']['cpu_percent'] > self.alert_thresholds['cpu_usage']:
            alerts.append({
                'type': 'cpu_high',
                'message': f"CPU使用率过高: {metrics['system']['cpu_percent']:.1f}%",
                'severity': 'warning'
            })
        
        # 内存使用率告警
        memory_percent = metrics['system']['memory']['percent']
        if memory_percent > self.alert_thresholds['memory_usage']:
            alerts.append({
                'type': 'memory_high',
                'message': f"内存使用率过高: {memory_percent:.1f}%",
                'severity': 'warning'
            })
        
        # 服务进程告警
        for service_name, service_info in metrics['services'].items():
            if service_info['cpu_percent'] > 50:
                alerts.append({
                    'type': 'service_cpu_high',
                    'message': f"服务 {service_name} CPU使用率过高: {service_info['cpu_percent']:.1f}%",
                    'severity': 'info'
                })
        
        # 端口健康检查告警
        for service_type, ports in self.service_ports.items():
            for port in ports:
                health = metrics['port_health'].get(str(port), {})
                if health.get('status') != 'healthy':
                    alerts.append({
                        'type': 'service_unhealthy',
                        'message': f"服务端口 {port} ({service_type}) 状态异常: {health.get('status', 'unknown')}",
                        'severity': 'critical'
                    })
        
        return alerts
    
    def collect_metrics(self):
        """收集所有指标"""
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'system': self.get_system_metrics(),
            'services': self.get_running_services(),
            'port_health': {},
            'log_analysis': {}
        }
        
        # 检查端口健康状态
        for service_type, ports in self.service_ports.items():
            for port in ports:
                metrics['port_health'][str(port)] = self.check_port_health(port)
        
        # 分析日志文件
        log_files = [
            'startup.log',
            'Analytics_7701.log',
            'Backend_8500.log',
            'Fight_3001.log',
            'Zone_1.log'
        ]
        
        for log_file in log_files:
            log_path = os.path.join(self.log_dir, log_file)
            metrics['log_analysis'][log_file] = self.analyze_log_errors(log_path)
        
        # 检查告警
        metrics['alerts'] = self.check_alerts(metrics)
        
        return metrics
    
    def save_metrics(self, metrics):
        """保存指标数据"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        metrics_file = os.path.join(self.monitor_dir, f'metrics_{timestamp}.json')
        
        try:
            os.makedirs(self.monitor_dir, exist_ok=True)
            with open(metrics_file, 'w') as f:
                json.dump(metrics, f, indent=2, ensure_ascii=False)
        except IOError as e:
            print(f"保存指标数据失败: {e}")
    
    def print_summary(self, metrics):
        """打印监控摘要"""
        print(f"\n{'='*60}")
        print(f"游戏服务器性能监控 - {metrics['timestamp']}")
        print(f"{'='*60}")
        
        # 系统指标
        system = metrics['system']
        print(f"系统指标:")
        print(f"  CPU使用率: {system['cpu_percent']:.1f}%")
        print(f"  内存使用率: {system['memory']['percent']:.1f}%")
        print(f"  磁盘使用率: {system['disk']['percent']:.1f}%")
        if system['load_average']:
            print(f"  系统负载: {system['load_average'][0]:.2f}")
        
        # 服务状态
        print(f"\n服务状态:")
        for service_name, service_info in metrics['services'].items():
            status = "运行中" if service_info else "已停止"
            if service_info:
                print(f"  {service_name}: {status} (CPU: {service_info['cpu_percent']:.1f}%, "
                      f"内存: {service_info['memory_percent']:.1f}%, "
                      f"线程: {service_info['num_threads']})")
            else:
                print(f"  {service_name}: {status}")
        
        # 端口健康状态
        print(f"\n端口健康状态:")
        for port, health in metrics['port_health'].items():
            status = health['status']
            response_time = health.get('response_time')
            if response_time:
                print(f"  端口 {port}: {status} (响应时间: {response_time:.1f}ms)")
            else:
                print(f"  端口 {port}: {status}")
        
        # 告警信息
        if metrics['alerts']:
            print(f"\n告警信息:")
            for alert in metrics['alerts']:
                severity_color = {
                    'info': '\033[94m',      # 蓝色
                    'warning': '\033[93m',   # 黄色
                    'critical': '\033[91m'   # 红色
                }
                color = severity_color.get(alert['severity'], '')
                reset_color = '\033[0m'
                print(f"  {color}[{alert['severity'].upper()}] {alert['message']}{reset_color}")
        else:
            print(f"\n✅ 无告警信息")
    
    def run_monitoring(self, interval=30):
        """运行监控循环"""
        print("开始游戏服务器性能监控...")
        print(f"监控间隔: {interval}秒")
        print("按 Ctrl+C 停止监控")
        
        try:
            while self.running:
                metrics = self.collect_metrics()
                self.save_metrics(metrics)
                self.print_summary(metrics)
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n监控已停止")
            self.running = False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='游戏服务器性能监控')
    parser.add_argument('--interval', type=int, default=30, help='监控间隔(秒)')
    parser.add_argument('--once', action='store_true', help='只运行一次')
    
    args = parser.parse_args()
    
    monitor = GameServerMonitor()
    
    if args.once:
        metrics = monitor.collect_metrics()
        monitor.save_metrics(metrics)
        monitor.print_summary(metrics)
    else:
        monitor.run_monitoring(args.interval)

if __name__ == '__main__':
    main()
