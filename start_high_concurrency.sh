#!/bin/bash

# 高并发优化的游戏服务器启动脚本
# 专门针对高并发场景优化，提升服务器处理能力

# 配置参数
LOG_DIR="/data/server/logs"
PID_DIR="/data/server/pids"
MONITOR_DIR="/data/server/monitor"

# 创建必要目录
mkdir -p "$LOG_DIR" "$PID_DIR" "$MONITOR_DIR"

# 高并发优化配置
# 针对8核16G服务器的高并发优化
BACKEND_PROCESSES=2      # 后台服务增加到2个进程
FIGHT_PROCESSES=2        # 战斗服务增加到2个进程（负载均衡）
ZONE_PROCESSES=1         # 区服保持单进程（避免数据冲突）

# 系统级优化设置
echo "应用系统级优化设置..."

# 增加文件描述符限制
ulimit -n 65536

# 增加进程数限制
ulimit -u 32768

# 设置TCP优化参数（需要root权限）
if [ "$EUID" -eq 0 ]; then
    echo "应用TCP优化参数..."
    # 增加TCP连接队列长度
    echo 8192 > /proc/sys/net/core/somaxconn
    # 优化TCP时间等待
    echo 1 > /proc/sys/net/ipv4/tcp_tw_reuse
    # 增加TCP缓冲区
    echo "4096 65536 16777216" > /proc/sys/net/ipv4/tcp_rmem
    echo "4096 65536 16777216" > /proc/sys/net/ipv4/tcp_wmem
fi

# 区服配置 - 保持原有端口配置
declare -A ZONE_CONFIG
ZONE_CONFIG[1]="5001"    # 1区 - 端口5001
ZONE_CONFIG[4]="5004"    # 4区 - 端口5004  
ZONE_CONFIG[5]="5005"    # 5区 - 端口5005

# 战斗服务器端口配置（负载均衡）
FIGHT_PORTS=(3001 3002)  # 两个战斗服务器端口

# 后台服务端口配置（负载均衡）
BACKEND_PORTS=(8500 8501)  # 两个后台服务端口

# 区服启动顺序
ZONE_START_ORDER=(1 4 5)

echo "高并发配置 - 后台服务: $BACKEND_PROCESSES, 战斗服务: $FIGHT_PROCESSES, 区服: $ZONE_PROCESSES"
echo "战斗服务端口: ${FIGHT_PORTS[@]}"
echo "后台服务端口: ${BACKEND_PORTS[@]}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

log_warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARN: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

# 检查进程是否运行
is_running() {
    local name="$1"
    local pid_file="$PID_DIR/${name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# 启动服务函数
start_service() {
    local name="$1"
    local cmd="$2"
    local port="$3"
    local log_file="$LOG_DIR/${name}.log"
    local pid_file="$PID_DIR/${name}.pid"
    local monitor_file="$MONITOR_DIR/${name}.monitor"

    log_info "检查服务 [$name]"

    if is_running "$name"; then
        log_warn "$name 已在运行，跳过启动"
        return 0
    fi

    log_info "启动 $name (端口: $port)..."
    
    # 启动服务并记录PID
    nohup $cmd >> "$log_file" 2>&1 &
    local pid=$!
    echo $pid > "$pid_file"
    
    # 等待服务启动
    sleep 2
    
    if kill -0 "$pid" 2>/dev/null; then
        log_info "$name 启动成功！PID: $pid"

        # 创建监控文件
        cat > "$monitor_file" << EOF
{
    "name": "$name",
    "pid": $pid,
    "port": $port,
    "start_time": "$(date '+%Y-%m-%d %H:%M:%S')",
    "command": "$cmd",
    "log_file": "$log_file"
}
EOF
        return 0
    else
        log_error "$name 启动失败！查看日志: tail -10 $log_file"
        rm -f "$pid_file"
        return 1
    fi
}

# 停止服务函数
stop_service() {
    local name="$1"
    local pid_file="$PID_DIR/${name}.pid"
    local monitor_file="$MONITOR_DIR/${name}.monitor"

    log_info "停止服务 [$name]"

    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            kill -TERM "$pid"
            sleep 2
            if kill -0 "$pid" 2>/dev/null; then
                kill -KILL "$pid"
            fi
        fi
        rm -f "$pid_file"
    fi

    # 清理监控文件
    rm -f "$monitor_file"

    log_info "$name 已停止"
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."
    
    # 检查内存
    local mem_total=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    local mem_used=$(free -m | awk 'NR==2{printf "%.0f", $3}')
    local mem_usage=$((mem_used * 100 / mem_total))
    
    log_info "内存使用情况: ${mem_used}MB/${mem_total}MB (${mem_usage}%)"
    
    # 检查CPU核心数
    local cpu_cores=$(nproc)
    log_info "CPU核心数: $cpu_cores"
    
    # 检查负载
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    log_info "系统负载: $load_avg"
    
    # 检查文件描述符限制
    local fd_limit=$(ulimit -n)
    log_info "文件描述符限制: $fd_limit"
}

# 显示服务状态
show_status() {
    log_info "高并发服务状态概览:"
    echo "----------------------------------------"
    printf "%-25s %-10s %-8s %-10s\n" "服务名称" "状态" "PID" "端口"
    echo "----------------------------------------"
    
    # 检查统计服务
    local pid=$(lsof -t -i:7701 2>/dev/null | head -1)
    if [ -n "$pid" ]; then
        printf "%-25s ${GREEN}%-10s${NC} %-8s %-10s\n" "Analytics_7701" "运行中" "$pid" "7701"
    else
        printf "%-25s ${RED}%-10s${NC} %-8s %-10s\n" "Analytics_7701" "已停止" "-" "7701"
    fi
    
    # 检查后台服务（多实例）
    for i in "${!BACKEND_PORTS[@]}"; do
        local port="${BACKEND_PORTS[$i]}"
        local service_name="Backend_${port}"
        local pid=$(lsof -t -i:$port 2>/dev/null | head -1)
        if [ -n "$pid" ]; then
            printf "%-25s ${GREEN}%-10s${NC} %-8s %-10s\n" "$service_name" "运行中" "$pid" "$port"
        else
            printf "%-25s ${RED}%-10s${NC} %-8s %-10s\n" "$service_name" "已停止" "-" "$port"
        fi
    done
    
    # 检查战斗服务（多实例）
    for i in "${!FIGHT_PORTS[@]}"; do
        local port="${FIGHT_PORTS[$i]}"
        local service_name="Fight_${port}"
        local pid=$(lsof -t -i:$port 2>/dev/null | head -1)
        if [ -n "$pid" ]; then
            printf "%-25s ${GREEN}%-10s${NC} %-8s %-10s\n" "$service_name" "运行中" "$pid" "$port"
        else
            printf "%-25s ${RED}%-10s${NC} %-8s %-10s\n" "$service_name" "已停止" "-" "$port"
        fi
    done
    
    # 检查区服
    for zone_id in "${!ZONE_CONFIG[@]}"; do
        local port="${ZONE_CONFIG[$zone_id]}"
        local service_name="Zone_${zone_id}"
        local pid=$(lsof -t -i:$port 2>/dev/null | head -1)
        if [ -n "$pid" ]; then
            printf "%-25s ${GREEN}%-10s${NC} %-8s %-10s\n" "$service_name" "运行中" "$pid" "$port"
        else
            printf "%-25s ${RED}%-10s${NC} %-8s %-10s\n" "$service_name" "已停止" "-" "$port"
        fi
    done
    
    # 检查备份服务
    local pid=$(pgrep -f "backup_start_sg3.py" 2>/dev/null | head -1)
    if [ -n "$pid" ]; then
        printf "%-25s ${GREEN}%-10s${NC} %-8s %-10s\n" "Backup_SG3" "运行中" "$pid" "0"
    else
        printf "%-25s ${RED}%-10s${NC} %-8s %-10s\n" "Backup_SG3" "已停止" "-" "0"
    fi
    
    echo "----------------------------------------"
}

# 主程序
ACTION="$1"

case "$ACTION" in
    "start")
        log_info "========== 开始启动高并发服务 =========="
        check_system_resources

        # 1. 启动统计服务
        start_service "Analytics_7701" "python /data/server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7701" "7701"

        # 2. 启动多个后台服务实例（负载均衡）
        for i in "${!BACKEND_PORTS[@]}"; do
            port="${BACKEND_PORTS[$i]}"
            log_info "启动后台服务实例 $((i+1)) (端口${port})..."
            start_service "Backend_${port}" "python /data/server/trunk/llol/src/manage.py runserver 0.0.0.0:${port}" "$port"
            sleep 1  # 避免同时启动造成资源竞争
        done

        # 3. 启动多个战斗服务实例（负载均衡）
        cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }
        for i in "${!FIGHT_PORTS[@]}"; do
            port="${FIGHT_PORTS[$i]}"
            log_info "启动战斗服务实例 $((i+1)) (端口${port})..."
            start_service "Fight_${port}" "node Laya.js ${port}" "$port"
            sleep 1  # 避免同时启动造成资源竞争
        done

        # 4. 启动备份服务
        start_service "Backup_SG3" "python backup_start_sg3.py" "0"

        # 5. 启动区服（保持单实例，避免数据冲突）
        log_info "开始启动区服..."
        for zone_id in "${ZONE_START_ORDER[@]}"; do
            if [[ -n "${ZONE_CONFIG[$zone_id]}" ]]; then
                port="${ZONE_CONFIG[$zone_id]}"
                log_info "启动${zone_id}区服务器 (端口${port})..."
                start_service "Zone_${zone_id}" "python server.py --zone=${zone_id}" "$port"
                sleep 3  # 避免数据库连接竞争
            fi
        done

        log_info "========== 高并发服务启动完成 =========="
        sleep 3
        show_status
        ;;

    "stop")
        log_info "========== 开始停止所有服务 =========="

        # 停止所有服务
        for pid_file in "$PID_DIR"/*.pid; do
            if [ -f "$pid_file" ]; then
                name=$(basename "$pid_file" .pid)
                stop_service "$name"
            fi
        done

        log_info "========== 所有服务已停止 =========="
        ;;

    "restart")
        log_info "========== 重启所有服务 =========="
        $0 stop
        sleep 3
        $0 start
        ;;

    "status")
        show_status
        check_system_resources
        ;;

    "monitor")
        # 实时监控模式
        log_info "进入实时监控模式 (按Ctrl+C退出)..."
        while true; do
            clear
            show_status
            check_system_resources
            sleep 5
        done
        ;;

    *)
        echo "高并发游戏服务器管理脚本"
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  start           - 启动所有高并发服务"
        echo "  stop            - 停止所有服务"
        echo "  restart         - 重启所有服务"
        echo "  status          - 显示服务状态"
        echo "  monitor         - 实时监控模式"
        echo ""
        echo "高并发配置:"
        echo "  后台服务: ${#BACKEND_PORTS[@]} 个实例 (端口: ${BACKEND_PORTS[@]})"
        echo "  战斗服务: ${#FIGHT_PORTS[@]} 个实例 (端口: ${FIGHT_PORTS[@]})"
        echo "  区服: ${#ZONE_CONFIG[@]} 个区服 (端口: ${ZONE_CONFIG[@]})"
        echo ""
        echo "优化特性:"
        echo "  - 增加了线程池大小"
        echo "  - 多实例负载均衡"
        echo "  - 系统级优化设置"
        echo "  - 资源监控"
        exit 1
        ;;
esac
