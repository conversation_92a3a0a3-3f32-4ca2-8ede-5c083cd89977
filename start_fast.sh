#!/bin/bash

# 快速启动版本 - 减少等待时间，保持原有功能
# 基于安全优化版本，但大幅减少启动等待时间

# 配置参数
LOG_DIR="/data/server/logs"
PID_DIR="/data/server/pids"
MONITOR_DIR="/data/server/monitor"

# 创建必要目录
mkdir -p "$LOG_DIR" "$PID_DIR" "$MONITOR_DIR"

# 快速启动配置
echo "应用快速启动优化..."

# 基础系统优化 (快速应用)
ulimit -n 65536
ulimit -u 32768

# 区服配置 - 包含6区
declare -A ZONE_CONFIG
ZONE_CONFIG[1]="5001"    # 1区 - 端口5001
ZONE_CONFIG[4]="5004"    # 4区 - 端口5004  
ZONE_CONFIG[5]="5005"    # 5区 - 端口5005
ZONE_CONFIG[6]="5006"    # 6区 - 端口5006

# 区服启动顺序
ZONE_START_ORDER=(1 4 5 6)

echo "快速启动配置："
echo "  保持单进程架构"
echo "  减少启动等待时间"
echo "  支持区服: ${!ZONE_CONFIG[@]}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

log_warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARN: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

# 检查进程是否运行
is_running() {
    local name="$1"
    local pid_file="$PID_DIR/${name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# 快速启动服务函数
start_service() {
    local name="$1"
    local cmd="$2"
    local port="$3"
    local log_file="$LOG_DIR/${name}.log"
    local pid_file="$PID_DIR/${name}.pid"

    if is_running "$name"; then
        log_warn "$name 已在运行，跳过启动"
        return 0
    fi

    log_info "快速启动 $name (端口: $port)..."
    
    # 启动服务并记录PID
    nohup $cmd >> "$log_file" 2>&1 &
    local pid=$!
    echo $pid > "$pid_file"
    
    # 快速检查 - 只等待0.5秒
    sleep 0.5
    
    if kill -0 "$pid" 2>/dev/null; then
        log_info "$name 启动成功！PID: $pid"
        return 0
    else
        log_error "$name 启动失败！查看日志: tail -10 $log_file"
        rm -f "$pid_file"
        return 1
    fi
}

# 停止服务函数
stop_service() {
    local name="$1"
    local pid_file="$PID_DIR/${name}.pid"

    log_info "停止服务 [$name]"

    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            kill -TERM "$pid"
            sleep 0.5  # 快速等待
            if kill -0 "$pid" 2>/dev/null; then
                kill -KILL "$pid"
            fi
        fi
        rm -f "$pid_file"
    fi

    log_info "$name 已停止"
}

# 显示服务状态
show_status() {
    log_info "快速启动服务状态:"
    echo "----------------------------------------"
    printf "%-20s %-10s %-8s %-10s\n" "服务名称" "状态" "PID" "端口"
    echo "----------------------------------------"
    
    # 检查统计服务
    local pid=$(lsof -t -i:7701 2>/dev/null | head -1)
    if [ -n "$pid" ]; then
        printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s\n" "Analytics_7701" "运行中" "$pid" "7701"
    else
        printf "%-20s ${RED}%-10s${NC} %-8s %-10s\n" "Analytics_7701" "已停止" "-" "7701"
    fi
    
    # 检查后台服务
    local pid=$(lsof -t -i:8500 2>/dev/null | head -1)
    if [ -n "$pid" ]; then
        printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s\n" "Backend_8500" "运行中" "$pid" "8500"
    else
        printf "%-20s ${RED}%-10s${NC} %-8s %-10s\n" "Backend_8500" "已停止" "-" "8500"
    fi
    
    # 检查战斗服务
    local pid=$(lsof -t -i:3001 2>/dev/null | head -1)
    if [ -n "$pid" ]; then
        printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s\n" "Fight_3001" "运行中" "$pid" "3001"
    else
        printf "%-20s ${RED}%-10s${NC} %-8s %-10s\n" "Fight_3001" "已停止" "-" "3001"
    fi
    
    # 检查区服
    for zone_id in "${!ZONE_CONFIG[@]}"; do
        local port="${ZONE_CONFIG[$zone_id]}"
        local service_name="Zone_${zone_id}"
        local pid=$(lsof -t -i:$port 2>/dev/null | head -1)
        if [ -n "$pid" ]; then
            printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s\n" "$service_name" "运行中" "$pid" "$port"
        else
            printf "%-20s ${RED}%-10s${NC} %-8s %-10s\n" "$service_name" "已停止" "-" "$port"
        fi
    done
    
    # 检查备份服务
    local pid=$(pgrep -f "backup_start_sg3.py" 2>/dev/null | head -1)
    if [ -n "$pid" ]; then
        printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s\n" "Backup_SG3" "运行中" "$pid" "0"
    else
        printf "%-20s ${RED}%-10s${NC} %-8s %-10s\n" "Backup_SG3" "已停止" "-" "0"
    fi
    
    echo "----------------------------------------"
}

# 主程序
ACTION="$1"

case "$ACTION" in
    "start")
        log_info "========== 开始快速启动服务 =========="
        
        # 1. 启动统计服务
        start_service "Analytics_7701" "python /data/server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7701" "7701"
        
        # 2. 启动后台服务
        start_service "Backend_8500" "python /data/server/trunk/llol/src/manage.py runserver 0.0.0.0:8500" "8500"
        
        # 3. 启动战斗服务
        cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }
        start_service "Fight_3001" "node Laya.js 3001" "3001"
        
        # 4. 启动备份服务
        start_service "Backup_SG3" "python backup_start_sg3.py" "0"
        
        # 5. 快速启动区服
        log_info "快速启动区服..."
        for zone_id in "${ZONE_START_ORDER[@]}"; do
            if [[ -n "${ZONE_CONFIG[$zone_id]}" ]]; then
                port="${ZONE_CONFIG[$zone_id]}"
                start_service "Zone_${zone_id}" "python server.py --zone=${zone_id}" "$port"
                # 不等待，直接启动下一个
            fi
        done
        
        log_info "========== 快速启动完成 =========="
        sleep 1  # 只等待1秒让服务稳定
        show_status
        ;;
        
    "stop")
        log_info "========== 快速停止所有服务 =========="
        
        # 并行停止所有服务
        for pid_file in "$PID_DIR"/*.pid; do
            if [ -f "$pid_file" ]; then
                name=$(basename "$pid_file" .pid)
                stop_service "$name" &  # 后台并行执行
            fi
        done
        
        wait  # 等待所有停止操作完成
        log_info "========== 所有服务已停止 =========="
        ;;

    "restart")
        log_info "========== 快速重启所有服务 =========="
        $0 stop
        sleep 1  # 只等待1秒
        $0 start
        ;;
        
    "status")
        show_status
        ;;

    "restart-zone")
        ZONE_ID="$2"
        if [ -z "$ZONE_ID" ]; then
            echo "用法: $0 restart-zone <区服ID>"
            echo "例如: $0 restart-zone 6"
            echo "支持的区服: ${!ZONE_CONFIG[@]}"
            exit 1
        fi

        if [[ -z "${ZONE_CONFIG[$ZONE_ID]}" ]]; then
            log_error "不支持的区服ID: $ZONE_ID"
            log_error "支持的区服ID: ${!ZONE_CONFIG[@]}"
            exit 1
        fi

        log_info "========== 快速重启区服 $ZONE_ID =========="

        # 快速停止和启动
        stop_service "Zone_$ZONE_ID"
        cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }
        
        PORT="${ZONE_CONFIG[$ZONE_ID]}"
        start_service "Zone_$ZONE_ID" "python server.py --zone=$ZONE_ID" "$PORT"

        log_info "========== 区服 $ZONE_ID 重启完成 =========="
        ;;

    "start-zone")
        ZONE_ID="$2"
        if [ -z "$ZONE_ID" ]; then
            echo "用法: $0 start-zone <区服ID>"
            echo "例如: $0 start-zone 6"
            echo "支持的区服: ${!ZONE_CONFIG[@]}"
            exit 1
        fi

        if [[ -z "${ZONE_CONFIG[$ZONE_ID]}" ]]; then
            log_error "不支持的区服ID: $ZONE_ID"
            log_error "支持的区服ID: ${!ZONE_CONFIG[@]}"
            exit 1
        fi

        log_info "========== 快速启动区服 $ZONE_ID =========="

        cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }

        PORT="${ZONE_CONFIG[$ZONE_ID]}"
        start_service "Zone_$ZONE_ID" "python server.py --zone=$ZONE_ID" "$PORT"

        log_info "========== 区服 $ZONE_ID 启动完成 =========="
        ;;

    "stop-zone")
        ZONE_ID="$2"
        if [ -z "$ZONE_ID" ]; then
            echo "用法: $0 stop-zone <区服ID>"
            echo "例如: $0 stop-zone 6"
            echo "支持的区服: ${!ZONE_CONFIG[@]}"
            exit 1
        fi

        if [[ -z "${ZONE_CONFIG[$ZONE_ID]}" ]]; then
            log_error "不支持的区服ID: $ZONE_ID"
            log_error "支持的区服ID: ${!ZONE_CONFIG[@]}"
            exit 1
        fi

        log_info "========== 快速停止区服 $ZONE_ID =========="

        stop_service "Zone_$ZONE_ID"

        log_info "========== 区服 $ZONE_ID 停止完成 =========="
        ;;

    *)
        echo "快速启动游戏服务器管理脚本"
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  start             - 快速启动所有服务"
        echo "  stop              - 快速停止所有服务"
        echo "  restart           - 快速重启所有服务"
        echo "  status            - 显示服务状态"
        echo ""
        echo "单区服命令:"
        echo "  start-zone <ID>   - 快速启动指定区服"
        echo "  stop-zone <ID>    - 快速停止指定区服"
        echo "  restart-zone <ID> - 快速重启指定区服"
        echo ""
        echo "支持的区服: ${!ZONE_CONFIG[@]}"
        echo ""
        echo "快速启动特性:"
        echo "  ✅ 大幅减少启动等待时间"
        echo "  ✅ 支持所有区服包括6区"
        echo "  ✅ 保持原有功能"
        echo "  ✅ 并行停止服务"
        echo ""
        echo "示例:"
        echo "  $0 start              # 快速启动所有服务"
        echo "  $0 start-zone 6       # 只启动6区"
        echo "  $0 stop-zone 6        # 只停止6区"
        echo "  $0 restart-zone 6     # 快速重启6区"
        echo "  $0 status             # 查看服务状态"
        exit 1
        ;;
esac
