#!/bin/bash

# 安全的游戏服务器优化启动脚本
# 只做系统级和配置级优化，不改变业务逻辑，风险最低

# 配置参数
LOG_DIR="/data/server/logs"
PID_DIR="/data/server/pids"
MONITOR_DIR="/data/server/monitor"

# 创建必要目录
mkdir -p "$LOG_DIR" "$PID_DIR" "$MONITOR_DIR"

# 安全优化配置 - 保持单进程，只优化系统参数
BACKEND_PROCESSES=1      # 保持单进程，避免数据冲突
FIGHT_PROCESSES=1        # 保持单进程，避免战斗结果不一致
ZONE_PROCESSES=1         # 保持单进程，避免游戏状态混乱

# 系统级安全优化 - 只优化不影响业务逻辑的参数
echo "应用安全的系统级优化..."

# 增加文件描述符限制 (安全)
ulimit -n 65536

# 增加进程数限制 (安全)
ulimit -u 32768

# 优化网络参数 (需要root权限，但不影响业务逻辑)
if [ "$EUID" -eq 0 ]; then
    echo "应用网络优化参数..."
    
    # 增加TCP连接队列长度 (安全)
    echo 8192 > /proc/sys/net/core/somaxconn
    
    # 优化TCP缓冲区 (安全)
    echo "4096 65536 16777216" > /proc/sys/net/ipv4/tcp_rmem
    echo "4096 65536 16777216" > /proc/sys/net/ipv4/tcp_wmem
    
    # 增加网络设备队列长度 (安全)
    echo 3000 > /proc/sys/net/core/netdev_max_backlog
    
    # 注意：不启用tcp_tw_reuse和tcp_tw_recycle，这些可能影响连接稳定性
fi

# 区服配置 - 保持原有配置并添加6区
declare -A ZONE_CONFIG
ZONE_CONFIG[1]="5001"    # 1区 - 端口5001
ZONE_CONFIG[4]="5004"    # 4区 - 端口5004
ZONE_CONFIG[5]="5005"    # 5区 - 端口5005
ZONE_CONFIG[6]="5006"    # 6区 - 端口5006 (新添加)

# 区服启动顺序
ZONE_START_ORDER=(1 4 5 6)

echo "安全优化配置："
echo "  保持单进程架构，避免数据竞争"
echo "  只优化系统参数和网络配置"
echo "  业务逻辑完全不变"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

log_warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARN: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

# 检查进程是否运行
is_running() {
    local name="$1"
    local pid_file="$PID_DIR/${name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# 启动服务函数 - 增加更多监控和错误处理
start_service() {
    local name="$1"
    local cmd="$2"
    local port="$3"
    local log_file="$LOG_DIR/${name}.log"
    local pid_file="$PID_DIR/${name}.pid"
    local monitor_file="$MONITOR_DIR/${name}.monitor"

    log_info "检查服务 [$name]"

    if is_running "$name"; then
        log_warn "$name 已在运行，跳过启动"
        return 0
    fi

    log_info "启动 $name (端口: $port)..."
    
    # 启动前检查端口是否被占用
    if [ "$port" != "0" ]; then
        local port_check=$(netstat -tlnp 2>/dev/null | grep ":$port ")
        if [ -n "$port_check" ]; then
            log_error "端口 $port 已被占用: $port_check"
            return 1
        fi
    fi
    
    # 启动服务并记录PID
    nohup $cmd >> "$log_file" 2>&1 &
    local pid=$!
    echo $pid > "$pid_file"
    
    # 等待服务启动，减少等待时间
    local check_count=0
    local max_checks=5  # 减少检查次数，从10减到5

    while [ $check_count -lt $max_checks ]; do
        sleep 0.5  # 减少等待时间，从1秒减到0.5秒
        if kill -0 "$pid" 2>/dev/null; then
            # 进程存在，检查端口是否监听
            if [ "$port" != "0" ]; then
                local port_listening=$(netstat -tlnp 2>/dev/null | grep ":$port " | grep "$pid")
                if [ -n "$port_listening" ]; then
                    break
                fi
            else
                break
            fi
        else
            log_error "$name 进程已退出，启动失败"
            rm -f "$pid_file"
            return 1
        fi
        check_count=$((check_count + 1))
    done
    
    if kill -0 "$pid" 2>/dev/null; then
        log_info "$name 启动成功！PID: $pid"

        # 创建详细的监控文件
        cat > "$monitor_file" << EOF
{
    "name": "$name",
    "pid": $pid,
    "port": $port,
    "start_time": "$(date '+%Y-%m-%d %H:%M:%S')",
    "command": "$cmd",
    "log_file": "$log_file",
    "optimization_level": "safe",
    "business_logic_changed": false
}
EOF
        return 0
    else
        log_error "$name 启动失败！查看日志: tail -20 $log_file"
        rm -f "$pid_file"
        return 1
    fi
}

# 停止服务函数 - 更安全的停止方式
stop_service() {
    local name="$1"
    local pid_file="$PID_DIR/${name}.pid"
    local monitor_file="$MONITOR_DIR/${name}.monitor"

    log_info "停止服务 [$name]"

    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "发送TERM信号给进程 $pid"
            kill -TERM "$pid"
            
            # 等待进程优雅退出
            local wait_count=0
            while [ $wait_count -lt 30 ]; do
                if ! kill -0 "$pid" 2>/dev/null; then
                    log_info "进程 $pid 已优雅退出"
                    break
                fi
                sleep 1
                wait_count=$((wait_count + 1))
            done
            
            # 如果还没退出，强制杀死
            if kill -0 "$pid" 2>/dev/null; then
                log_warn "进程 $pid 未响应TERM信号，发送KILL信号"
                kill -KILL "$pid"
                sleep 1
            fi
        fi
        rm -f "$pid_file"
    fi

    # 清理监控文件
    rm -f "$monitor_file"

    log_info "$name 已停止"
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."
    
    # 检查内存
    local mem_info=$(free -m)
    local mem_total=$(echo "$mem_info" | awk 'NR==2{printf "%.0f", $2}')
    local mem_used=$(echo "$mem_info" | awk 'NR==2{printf "%.0f", $3}')
    local mem_usage=$((mem_used * 100 / mem_total))
    
    log_info "内存使用情况: ${mem_used}MB/${mem_total}MB (${mem_usage}%)"
    
    if [ $mem_usage -gt 80 ]; then
        log_warn "内存使用率过高: ${mem_usage}%"
    fi
    
    # 检查CPU核心数和负载
    local cpu_cores=$(nproc)
    log_info "CPU核心数: $cpu_cores"
    
    if command -v uptime >/dev/null 2>&1; then
        local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
        log_info "系统负载: $load_avg"
        
        # 负载告警
        local load_threshold=$((cpu_cores * 2))
        if (( $(echo "$load_avg > $load_threshold" | bc -l 2>/dev/null || echo "0") )); then
            log_warn "系统负载过高: $load_avg (建议小于 $load_threshold)"
        fi
    fi
    
    # 检查磁盘空间
    local disk_usage=$(df -h /data 2>/dev/null | awk 'NR==2 {print $5}' | sed 's/%//' || echo "0")
    log_info "磁盘使用率: ${disk_usage}%"
    
    if [ "$disk_usage" -gt 80 ]; then
        log_warn "磁盘使用率过高: ${disk_usage}%"
    fi
    
    # 检查文件描述符限制
    local fd_limit=$(ulimit -n)
    log_info "文件描述符限制: $fd_limit"
    
    if [ "$fd_limit" -lt 10000 ]; then
        log_warn "文件描述符限制较低: $fd_limit (建议至少10000)"
    fi
}

# 显示服务状态
show_status() {
    log_info "安全优化服务状态概览:"
    echo "----------------------------------------"
    printf "%-20s %-10s %-8s %-10s %-15s\n" "服务名称" "状态" "PID" "端口" "优化级别"
    echo "----------------------------------------"
    
    # 检查统计服务
    local pid=$(lsof -t -i:7701 2>/dev/null | head -1)
    if [ -n "$pid" ]; then
        printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s %-15s\n" "Analytics_7701" "运行中" "$pid" "7701" "安全优化"
    else
        printf "%-20s ${RED}%-10s${NC} %-8s %-10s %-15s\n" "Analytics_7701" "已停止" "-" "7701" "安全优化"
    fi
    
    # 检查后台服务
    local pid=$(lsof -t -i:8500 2>/dev/null | head -1)
    if [ -n "$pid" ]; then
        printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s %-15s\n" "Backend_8500" "运行中" "$pid" "8500" "安全优化"
    else
        printf "%-20s ${RED}%-10s${NC} %-8s %-10s %-15s\n" "Backend_8500" "已停止" "-" "8500" "安全优化"
    fi
    
    # 检查战斗服务
    local pid=$(lsof -t -i:3001 2>/dev/null | head -1)
    if [ -n "$pid" ]; then
        printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s %-15s\n" "Fight_3001" "运行中" "$pid" "3001" "安全优化"
    else
        printf "%-20s ${RED}%-10s${NC} %-8s %-10s %-15s\n" "Fight_3001" "已停止" "-" "3001" "安全优化"
    fi
    
    # 检查区服
    for zone_id in "${!ZONE_CONFIG[@]}"; do
        local port="${ZONE_CONFIG[$zone_id]}"
        local service_name="Zone_${zone_id}"
        local pid=$(lsof -t -i:$port 2>/dev/null | head -1)
        if [ -n "$pid" ]; then
            printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s %-15s\n" "$service_name" "运行中" "$pid" "$port" "安全优化"
        else
            printf "%-20s ${RED}%-10s${NC} %-8s %-10s %-15s\n" "$service_name" "已停止" "-" "$port" "安全优化"
        fi
    done
    
    # 检查备份服务
    local pid=$(pgrep -f "backup_start_sg3.py" 2>/dev/null | head -1)
    if [ -n "$pid" ]; then
        printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s %-15s\n" "Backup_SG3" "运行中" "$pid" "0" "安全优化"
    else
        printf "%-20s ${RED}%-10s${NC} %-8s %-10s %-15s\n" "Backup_SG3" "已停止" "-" "0" "安全优化"
    fi
    
    echo "----------------------------------------"
    log_info "所有服务均为单进程模式，业务逻辑未改变"
}

# 主程序
ACTION="$1"

case "$ACTION" in
    "start")
        log_info "========== 开始启动安全优化服务 =========="
        check_system_resources

        # 1. 启动统计服务
        start_service "Analytics_7701" "python /data/server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7701" "7701"

        # 2. 启动后台服务 (单进程)
        start_service "Backend_8500" "python /data/server/trunk/llol/src/manage.py runserver 0.0.0.0:8500" "8500"

        # 3. 启动战斗服务 (单进程)
        cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }
        start_service "Fight_3001" "node Laya.js 3001" "3001"

        # 4. 启动备份服务
        start_service "Backup_SG3" "python backup_start_sg3.py" "0"

        # 5. 启动区服 (单进程)
        log_info "开始启动区服..."
        for zone_id in "${ZONE_START_ORDER[@]}"; do
            if [[ -n "${ZONE_CONFIG[$zone_id]}" ]]; then
                port="${ZONE_CONFIG[$zone_id]}"
                log_info "启动${zone_id}区服务器 (端口${port})..."
                start_service "Zone_${zone_id}" "python server.py --zone=${zone_id}" "$port"
                sleep 1  # 减少等待时间，从3秒减到1秒
            fi
        done

        log_info "========== 安全优化服务启动完成 =========="
        sleep 3
        show_status

        log_info "安全优化说明："
        log_info "  ✅ 保持单进程架构，避免数据竞争"
        log_info "  ✅ 只优化系统参数，业务逻辑不变"
        log_info "  ✅ 可以安全回滚到原版本"
        ;;

    "stop")
        log_info "========== 开始停止所有服务 =========="

        # 停止所有服务
        for pid_file in "$PID_DIR"/*.pid; do
            if [ -f "$pid_file" ]; then
                name=$(basename "$pid_file" .pid)
                stop_service "$name"
            fi
        done

        log_info "========== 所有服务已停止 =========="
        ;;

    "restart")
        log_info "========== 重启所有服务 =========="
        $0 stop
        sleep 3
        $0 start
        ;;

    "status")
        show_status
        check_system_resources
        ;;

    "monitor")
        # 实时监控模式
        log_info "进入实时监控模式 (按Ctrl+C退出)..."
        while true; do
            clear
            show_status
            check_system_resources
            sleep 5
        done
        ;;

    "restart-zone")
        ZONE_ID="$2"
        if [ -z "$ZONE_ID" ]; then
            echo "用法: $0 restart-zone <区服ID>"
            echo "例如: $0 restart-zone 6"
            echo "支持的区服: ${!ZONE_CONFIG[@]}"
            exit 1
        fi

        log_info "========== 重启区服 $ZONE_ID =========="

        # 检查区服ID是否有效
        if [[ -z "${ZONE_CONFIG[$ZONE_ID]}" ]]; then
            log_error "不支持的区服ID: $ZONE_ID"
            log_error "支持的区服ID: ${!ZONE_CONFIG[@]}"
            log_error "区服配置："
            for zone_id in "${!ZONE_CONFIG[@]}"; do
                log_error "  ${zone_id}区 - 端口${ZONE_CONFIG[$zone_id]}"
            done
            exit 1
        fi

        # 停止指定区服
        stop_service "Zone_$ZONE_ID"
        sleep 1  # 减少等待时间

        # 启动指定区服
        cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }

        # 获取区服端口号
        PORT="${ZONE_CONFIG[$ZONE_ID]}"
        log_info "重启${ZONE_ID}区服务器 (端口${PORT})..."
        start_service "Zone_$ZONE_ID" "python server.py --zone=$ZONE_ID" "$PORT"

        log_info "========== 区服 $ZONE_ID 重启完成 =========="
        ;;

    "restart-fight")
        FIGHT_PORT="$2"
        if [ -z "$FIGHT_PORT" ]; then
            echo "用法: $0 restart-fight <端口>"
            echo "例如: $0 restart-fight 3001"
            exit 1
        fi

        log_info "========== 重启战斗服 $FIGHT_PORT =========="

        # 停止指定战斗服
        stop_service "Fight_$FIGHT_PORT"
        sleep 1

        # 启动指定战斗服
        cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }
        start_service "Fight_$FIGHT_PORT" "node Laya.js $FIGHT_PORT" "$FIGHT_PORT"

        log_info "========== 战斗服 $FIGHT_PORT 重启完成 =========="
        ;;

    "restart-backend")
        log_info "========== 重启后台服务 =========="

        # 停止后台服务
        stop_service "Backend_8500"
        sleep 1

        # 启动后台服务
        start_service "Backend_8500" "python /data/server/trunk/llol/src/manage.py runserver 0.0.0.0:8500" "8500"

        log_info "========== 后台服务重启完成 =========="
        ;;

    "start-zone")
        ZONE_ID="$2"
        if [ -z "$ZONE_ID" ]; then
            echo "用法: $0 start-zone <区服ID>"
            echo "例如: $0 start-zone 6"
            echo "支持的区服: ${!ZONE_CONFIG[@]}"
            exit 1
        fi

        # 检查区服ID是否有效
        if [[ -z "${ZONE_CONFIG[$ZONE_ID]}" ]]; then
            log_error "不支持的区服ID: $ZONE_ID"
            log_error "支持的区服ID: ${!ZONE_CONFIG[@]}"
            exit 1
        fi

        log_info "========== 启动区服 $ZONE_ID =========="

        # 进入服务目录
        cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }

        # 获取区服端口号
        PORT="${ZONE_CONFIG[$ZONE_ID]}"
        log_info "启动${ZONE_ID}区服务器 (端口${PORT})..."
        start_service "Zone_$ZONE_ID" "python server.py --zone=$ZONE_ID" "$PORT"

        log_info "========== 区服 $ZONE_ID 启动完成 =========="
        ;;

    "stop-zone")
        ZONE_ID="$2"
        if [ -z "$ZONE_ID" ]; then
            echo "用法: $0 stop-zone <区服ID>"
            echo "例如: $0 stop-zone 6"
            echo "支持的区服: ${!ZONE_CONFIG[@]}"
            exit 1
        fi

        # 检查区服ID是否有效
        if [[ -z "${ZONE_CONFIG[$ZONE_ID]}" ]]; then
            log_error "不支持的区服ID: $ZONE_ID"
            log_error "支持的区服ID: ${!ZONE_CONFIG[@]}"
            exit 1
        fi

        log_info "========== 停止区服 $ZONE_ID =========="

        # 停止指定区服
        stop_service "Zone_$ZONE_ID"

        log_info "========== 区服 $ZONE_ID 停止完成 =========="
        ;;

    "rollback")
        log_info "========== 回滚到原版本 =========="
        $0 stop
        sleep 2

        # 恢复原始启动脚本
        if [ -f "start_backup.sh" ]; then
            log_info "使用备份的启动脚本"
            ./start_backup.sh start
        elif [ -f "start.sh" ]; then
            log_info "使用原始启动脚本"
            ./start.sh start
        else
            log_error "找不到原始启动脚本"
            exit 1
        fi
        ;;

    *)
        echo "安全优化游戏服务器管理脚本"
        echo "用法: $0 [命令]"
        echo ""
        echo "命令:"
        echo "  start           - 启动安全优化服务"
        echo "  stop            - 停止所有服务"
        echo "  restart         - 重启所有服务"
        echo "  status          - 显示服务状态"
        echo "  monitor         - 实时监控模式"
        echo "  rollback        - 回滚到原版本"
        echo ""
        echo "单服务命令:"
        echo "  start-zone <ID>       - 启动指定区服 (支持区服: ${!ZONE_CONFIG[@]})"
        echo "  stop-zone <ID>        - 停止指定区服"
        echo "  restart-zone <ID>     - 重启指定区服"
        echo "  restart-fight <PORT>  - 重启指定战斗服 (例如: restart-fight 3001)"
        echo "  restart-backend       - 重启后台服务"
        echo ""
        echo "安全优化特性:"
        echo "  ✅ 保持单进程架构，避免数据竞争"
        echo "  ✅ 只优化系统参数和网络配置"
        echo "  ✅ 业务逻辑完全不变"
        echo "  ✅ 可以安全回滚"
        echo "  ✅ 增强错误检查和监控"
        echo ""
        echo "示例:"
        echo "  $0 start              # 启动所有服务"
        echo "  $0 start-zone 6       # 只启动6区"
        echo "  $0 stop-zone 6        # 只停止6区"
        echo "  $0 restart-zone 6     # 重启6区"
        echo "  $0 status             # 查看状态"
        echo ""
        echo "风险评估: 极低 (只改系统配置，不改业务逻辑)"
        exit 1
        ;;
esac
