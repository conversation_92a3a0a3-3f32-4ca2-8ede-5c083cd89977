#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
游戏性能测试脚本
测试优化前后的实际游戏响应时间
"""

import time
import json
import requests
import websocket
import threading
from datetime import datetime
import statistics

class GamePerformanceTester:
    def __init__(self, server_host="127.0.0.1", server_port=5001):
        self.server_host = server_host
        self.server_port = server_port
        self.ws_url = f"ws://{server_host}:{server_port}/gateway/"
        self.api_url = f"http://{server_host}:8500/api/"
        
        self.test_results = {
            'login_times': [],
            'chat_times': [],
            'battle_times': [],
            'api_times': [],
            'connection_times': []
        }
    
    def test_websocket_connection(self):
        """测试WebSocket连接时间"""
        print("测试WebSocket连接时间...")
        
        for i in range(10):
            start_time = time.time()
            try:
                ws = websocket.create_connection(self.ws_url, timeout=10)
                connection_time = (time.time() - start_time) * 1000
                self.test_results['connection_times'].append(connection_time)
                ws.close()
                print(f"连接 {i+1}: {connection_time:.1f}ms")
                time.sleep(0.5)
            except Exception as e:
                print(f"连接 {i+1} 失败: {e}")
                self.test_results['connection_times'].append(5000)  # 5秒超时
    
    def test_login_response(self):
        """测试登录响应时间"""
        print("测试登录响应时间...")
        
        # 模拟登录请求
        login_data = {
            "pid": 1,
            "phone_id": "test_phone",
            "method": "login",
            "params": {
                "uid": 12345,
                "sessionid": "test_session",
                "zone": 1,
                "pf": "test"
            }
        }
        
        for i in range(5):
            start_time = time.time()
            try:
                ws = websocket.create_connection(self.ws_url, timeout=10)
                ws.send(json.dumps(login_data))
                response = ws.recv()
                response_time = (time.time() - start_time) * 1000
                self.test_results['login_times'].append(response_time)
                ws.close()
                print(f"登录 {i+1}: {response_time:.1f}ms")
                time.sleep(1)
            except Exception as e:
                print(f"登录 {i+1} 失败: {e}")
                self.test_results['login_times'].append(5000)
    
    def test_chat_response(self):
        """测试聊天响应时间"""
        print("测试聊天响应时间...")
        
        chat_data = {
            "pid": 2,
            "phone_id": "test_phone",
            "method": "chat",
            "params": {
                "content": "测试消息",
                "channel": "world"
            }
        }
        
        for i in range(10):
            start_time = time.time()
            try:
                ws = websocket.create_connection(self.ws_url, timeout=10)
                ws.send(json.dumps(chat_data))
                response = ws.recv()
                response_time = (time.time() - start_time) * 1000
                self.test_results['chat_times'].append(response_time)
                ws.close()
                print(f"聊天 {i+1}: {response_time:.1f}ms")
                time.sleep(0.3)
            except Exception as e:
                print(f"聊天 {i+1} 失败: {e}")
                self.test_results['chat_times'].append(5000)
    
    def test_api_response(self):
        """测试API响应时间"""
        print("测试API响应时间...")
        
        for i in range(10):
            start_time = time.time()
            try:
                response = requests.get(f"http://{self.server_host}:{self.server_port}/ip/", timeout=10)
                response_time = (time.time() - start_time) * 1000
                self.test_results['api_times'].append(response_time)
                print(f"API {i+1}: {response_time:.1f}ms")
                time.sleep(0.2)
            except Exception as e:
                print(f"API {i+1} 失败: {e}")
                self.test_results['api_times'].append(5000)
    
    def test_concurrent_connections(self, num_connections=20):
        """测试并发连接"""
        print(f"测试 {num_connections} 个并发连接...")
        
        results = []
        threads = []
        
        def connect_test():
            start_time = time.time()
            try:
                ws = websocket.create_connection(self.ws_url, timeout=10)
                connection_time = (time.time() - start_time) * 1000
                results.append(connection_time)
                time.sleep(2)  # 保持连接2秒
                ws.close()
            except Exception as e:
                results.append(5000)
        
        # 启动并发连接
        start_time = time.time()
        for i in range(num_connections):
            thread = threading.Thread(target=connect_test)
            threads.append(thread)
            thread.start()
            time.sleep(0.1)  # 稍微错开启动时间
        
        # 等待所有连接完成
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        success_count = len([r for r in results if r < 5000])
        
        print(f"并发测试结果:")
        print(f"  总时间: {total_time:.1f}秒")
        print(f"  成功连接: {success_count}/{num_connections}")
        print(f"  平均连接时间: {statistics.mean(results):.1f}ms")
        
        return results
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("游戏性能测试报告")
        print("="*60)
        
        # 连接时间统计
        if self.test_results['connection_times']:
            conn_times = [t for t in self.test_results['connection_times'] if t < 5000]
            if conn_times:
                print(f"WebSocket连接时间:")
                print(f"  平均: {statistics.mean(conn_times):.1f}ms")
                print(f"  最快: {min(conn_times):.1f}ms")
                print(f"  最慢: {max(conn_times):.1f}ms")
                print(f"  成功率: {len(conn_times)}/{len(self.test_results['connection_times'])} ({len(conn_times)/len(self.test_results['connection_times'])*100:.1f}%)")
        
        # 登录时间统计
        if self.test_results['login_times']:
            login_times = [t for t in self.test_results['login_times'] if t < 5000]
            if login_times:
                print(f"\n登录响应时间:")
                print(f"  平均: {statistics.mean(login_times):.1f}ms")
                print(f"  最快: {min(login_times):.1f}ms")
                print(f"  最慢: {max(login_times):.1f}ms")
        
        # 聊天时间统计
        if self.test_results['chat_times']:
            chat_times = [t for t in self.test_results['chat_times'] if t < 5000]
            if chat_times:
                print(f"\n聊天响应时间:")
                print(f"  平均: {statistics.mean(chat_times):.1f}ms")
                print(f"  最快: {min(chat_times):.1f}ms")
                print(f"  最慢: {max(chat_times):.1f}ms")
        
        # API时间统计
        if self.test_results['api_times']:
            api_times = [t for t in self.test_results['api_times'] if t < 5000]
            if api_times:
                print(f"\nAPI响应时间:")
                print(f"  平均: {statistics.mean(api_times):.1f}ms")
                print(f"  最快: {min(api_times):.1f}ms")
                print(f"  最慢: {max(api_times):.1f}ms")
        
        # 性能评级
        print(f"\n性能评级:")
        avg_response = 0
        count = 0
        
        for times in self.test_results.values():
            valid_times = [t for t in times if t < 5000]
            if valid_times:
                avg_response += statistics.mean(valid_times)
                count += 1
        
        if count > 0:
            overall_avg = avg_response / count
            if overall_avg < 200:
                grade = "优秀 🟢"
            elif overall_avg < 500:
                grade = "良好 🟡"
            elif overall_avg < 1000:
                grade = "一般 🟠"
            else:
                grade = "需要优化 🔴"
            
            print(f"  整体平均响应时间: {overall_avg:.1f}ms")
            print(f"  性能等级: {grade}")
        
        # 保存详细结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"performance_report_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': timestamp,
                'test_results': self.test_results,
                'summary': {
                    'overall_avg': overall_avg if count > 0 else 0,
                    'grade': grade if count > 0 else 'N/A'
                }
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n详细报告已保存到: {report_file}")
    
    def run_full_test(self):
        """运行完整测试"""
        print("开始游戏性能测试...")
        print(f"测试服务器: {self.server_host}:{self.server_port}")
        print("-" * 60)
        
        try:
            # 基础连接测试
            self.test_websocket_connection()
            time.sleep(1)
            
            # API响应测试
            self.test_api_response()
            time.sleep(1)
            
            # 功能响应测试
            # self.test_login_response()  # 需要有效的登录数据
            # self.test_chat_response()   # 需要登录状态
            
            # 并发测试
            self.test_concurrent_connections(10)
            
        except KeyboardInterrupt:
            print("\n测试被用户中断")
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
        
        # 生成报告
        self.generate_report()

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='游戏性能测试工具')
    parser.add_argument('--host', default='127.0.0.1', help='服务器地址')
    parser.add_argument('--port', type=int, default=5001, help='服务器端口')
    parser.add_argument('--quick', action='store_true', help='快速测试模式')
    
    args = parser.parse_args()
    
    tester = GamePerformanceTester(args.host, args.port)
    
    if args.quick:
        print("快速测试模式...")
        tester.test_websocket_connection()
        tester.test_api_response()
        tester.generate_report()
    else:
        tester.run_full_test()

if __name__ == '__main__':
    main()
