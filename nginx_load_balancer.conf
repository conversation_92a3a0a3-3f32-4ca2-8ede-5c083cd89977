# Nginx负载均衡配置
# 用于在多个游戏服务实例之间分发请求

# 后台服务负载均衡池
upstream backend_pool {
    # 使用ip_hash确保同一用户的请求总是路由到同一台服务器
    ip_hash;
    
    server 127.0.0.1:8500 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8501 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8502 weight=1 max_fails=3 fail_timeout=30s;
    
    # 备用服务器
    # server 127.0.0.1:8503 backup;
}

# 战斗服务负载均衡池
upstream fight_pool {
    # 使用least_conn最少连接数算法，适合计算密集型任务
    least_conn;
    
    server 127.0.0.1:3001 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3002 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3003 weight=1 max_fails=3 fail_timeout=30s;
}

# 1区服务负载均衡池
upstream zone1_pool {
    ip_hash;
    
    server 127.0.0.1:5001 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:5011 weight=1 max_fails=3 fail_timeout=30s;
}

# 4区服务负载均衡池
upstream zone4_pool {
    ip_hash;
    
    server 127.0.0.1:5004 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:5014 weight=1 max_fails=3 fail_timeout=30s;
}

# 5区服务负载均衡池
upstream zone5_pool {
    ip_hash;
    
    server 127.0.0.1:5005 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:5015 weight=1 max_fails=3 fail_timeout=30s;
}

# 主服务器配置
server {
    listen 80;
    server_name your-game-server.com;
    
    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # 设置客户端最大请求体大小
    client_max_body_size 10M;
    
    # 设置超时时间
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    # 后台管理接口
    location /admin/ {
        proxy_pass http://backend_pool;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 启用缓存
        proxy_cache_bypass $http_upgrade;
        proxy_cache_valid 200 302 10m;
        proxy_cache_valid 404 1m;
    }
    
    # 战斗服务接口
    location /fight/ {
        proxy_pass http://fight_pool;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 战斗服务不缓存，确保实时性
        proxy_cache off;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
    
    # 1区游戏服务
    location /zone1/ {
        proxy_pass http://zone1_pool;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 4区游戏服务
    location /zone4/ {
        proxy_pass http://zone4_pool;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 5区游戏服务
    location /zone5/ {
        proxy_pass http://zone5_pool;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 静态资源
    location /static/ {
        alias /data/server/static/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
    
    # 健康检查接口
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 服务状态监控
    location /status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        deny all;
    }
}

# HTTPS配置（如果需要）
server {
    listen 443 ssl http2;
    server_name your-game-server.com;
    
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL优化配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 其他配置与HTTP相同
    # ... (复制上面的location配置)
}

# 日志格式
log_format game_access '$remote_addr - $remote_user [$time_local] '
                      '"$request" $status $body_bytes_sent '
                      '"$http_referer" "$http_user_agent" '
                      '$request_time $upstream_response_time '
                      '$upstream_addr $upstream_status';

# 访问日志
access_log /var/log/nginx/game_access.log game_access;
error_log /var/log/nginx/game_error.log warn;

# 限制请求频率（防止DDoS）
limit_req_zone $binary_remote_addr zone=game_limit:10m rate=10r/s;
limit_req zone=game_limit burst=20 nodelay;

# 限制连接数
limit_conn_zone $binary_remote_addr zone=conn_limit:10m;
limit_conn conn_limit 20;
